"""
Configuration settings for the Career Guidance AI Assistant
"""
import os
from dotenv import load_dotenv

load_dotenv()

# OpenAI API Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
OPENAI_MODEL = "gpt-4"
OPENAI_TEMPERATURE = 0.7
OPENAI_MAX_TOKENS = 1000

# Application Settings
APP_TITLE = "求职助手 AI - Career Guidance Assistant"
APP_DESCRIPTION = "基于大模型的智能求职规划助手"

# User Profile Settings
USER_PROFILE_FILE = "user_profile.json"
DEFAULT_USER_PROFILE = {
    "user_id": "default_user",
    "基本信息": {
        "姓名": "",
        "年龄": 0,
        "学历": "",
        "专业": "",
        "毕业时间": "",
        "所在地": ""
    },
    "求职意向": {
        "目标行业": "",
        "目标岗位": "",
        "期望薪资": "",
        "求职状态": ""
    },
    "技能与兴趣": {
        "已有技能": [],
        "项目经历": [],
        "兴趣方向": []
    },
    "行为数据": {
        "历史对话": [],
        "最近更新时间": ""
    }
}

# Intent Classification Keywords
INTENT_KEYWORDS = {
    "career": ["规划", "职业路径", "发展", "职业", "规划", "路径", "发展方向", "职业发展"],
    "skill": ["学", "学习", "掌握", "提升", "教程", "练习", "技能", "能力", "培训", "课程"],
    "industry": ["行业", "前景", "市场", "需求", "薪资", "岗位", "趋势", "就业", "招聘"]
}

# Visualization Settings
RADAR_CHART_SKILLS = {
    "前端开发": {
        "HTML/CSS": 4,
        "JavaScript": 5,
        "React/Vue": 4,
        "Node.js": 3,
        "工程化工具": 3,
        "沟通能力": 3
    },
    "后端开发": {
        "编程语言": 5,
        "数据库": 4,
        "系统设计": 4,
        "API设计": 4,
        "性能优化": 3,
        "团队协作": 3
    },
    "数据分析": {
        "Python/R": 5,
        "SQL": 4,
        "统计学": 4,
        "机器学习": 3,
        "数据可视化": 4,
        "业务理解": 4
    }
}
