"""
Industry Expert Agent for Career Guidance AI Assistant
"""
import json
import logging
from typing import Dict, List, Optional
from utils.llm_client import llm_client
from utils.helpers import format_user_profile_for_prompt, split_text_and_json

logger = logging.getLogger(__name__)

class IndustryExpertAgent:
    """Specialized agent for industry insights, trends, and market analysis"""
    
    def __init__(self):
        self.system_prompt = """你是一名行业资深专家，熟悉各大行业的宏观趋势、岗位分布、薪资水平、技能需求等。

请用专业的视角给出简洁、全面的回答，包括：
1. 行业现状和发展趋势
2. 主要岗位类型和职责
3. 技能要求和能力模型
4. 薪资水平参考（注明"参考值，以实际招聘为准"）
5. 就业前景和建议

如果可能，请在最后提供一个JSON格式的行业概览，格式如下：
{
  "行业名称": "具体行业",
  "发展趋势": ["趋势1", "趋势2"],
  "主要岗位": ["岗位1", "岗位2"],
  "核心技能": ["技能1", "技能2"],
  "参考薪资范围": "X-Y万（地区/经验说明）",
  "就业前景": "前景描述"
}"""
    
    def process_request(self, user_message: str, user_profile: Dict) -> Dict:
        """
        Process industry expert request
        
        Args:
            user_message: User's industry-related question
            user_profile: User's profile information
            
        Returns:
            Dictionary containing response text and structured industry summary
        """
        try:
            if not llm_client:
                return {
                    "text": "抱歉，AI服务暂时不可用，请检查API配置。",
                    "summary": {}
                }
            
            # Extract relevant user information
            target_industry = user_profile.get("求职意向", {}).get("目标行业", "")
            location = user_profile.get("基本信息", {}).get("所在地", "")
            target_position = user_profile.get("求职意向", {}).get("目标岗位", "")
            
            # Construct user prompt
            user_prompt = f"""以下是用户信息：
目标行业：{target_industry}
目标岗位：{target_position}
所在城市：{location}

用户问题：{user_message}

请基于用户的背景和问题，提供专业、详细的行业分析和建议。如果用户询问薪资，请给出参考范围并注明地区和经验要求。"""
            
            # Call LLM
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = llm_client.chat_completion(messages)
            
            # Split text and JSON parts
            text_part, json_part = split_text_and_json(response)
            
            # Parse JSON structure
            industry_summary = {}
            if json_part:
                try:
                    industry_summary = json.loads(json_part)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON from LLM response: {e}")
            
            return {
                "text": text_part if text_part else response,
                "summary": industry_summary
            }
            
        except Exception as e:
            logger.error(f"Error in industry expert agent: {e}")
            return {
                "text": f"处理行业咨询请求时出现错误：{str(e)}",
                "summary": {}
            }
    
    def get_industry_trends(self, industry: str) -> Dict:
        """
        Get current trends for a specific industry
        
        Args:
            industry: Industry name
            
        Returns:
            Dictionary containing industry trends
        """
        try:
            if not llm_client:
                return {"trends": ["AI服务暂时不可用"], "analysis": ""}
            
            prompt = f"""请分析{industry}行业的最新发展趋势，包括：

1. 技术发展趋势
2. 市场需求变化
3. 新兴岗位和机会
4. 行业挑战和风险
5. 未来3-5年发展预测

请提供具体、有价值的分析。"""
            
            messages = [
                {"role": "system", "content": "你是行业趋势分析专家，请提供准确的行业发展趋势分析。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.6)
            
            # Extract trends from response
            trends = []
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and (line.startswith(('1.', '2.', '3.', '4.', '5.')) or '趋势' in line):
                    trends.append(line)
            
            return {
                "trends": trends[:5],  # Top 5 trends
                "analysis": response
            }
            
        except Exception as e:
            logger.error(f"Error getting industry trends: {e}")
            return {
                "trends": [f"获取行业趋势时出现错误：{str(e)}"],
                "analysis": ""
            }
    
    def get_salary_analysis(self, position: str, location: str = "", experience: str = "") -> Dict:
        """
        Get salary analysis for a specific position
        
        Args:
            position: Job position
            location: Location (optional)
            experience: Experience level (optional)
            
        Returns:
            Dictionary containing salary analysis
        """
        try:
            if not llm_client:
                return {"range": "AI服务暂时不可用", "factors": [], "analysis": ""}
            
            location_text = f"在{location}" if location else ""
            experience_text = f"，{experience}经验水平" if experience else ""
            
            prompt = f"""请分析{position}岗位{location_text}的薪资水平{experience_text}，包括：

1. 薪资范围（月薪/年薪）
2. 影响薪资的主要因素
3. 不同经验水平的薪资差异
4. 行业内薪资对比
5. 薪资发展趋势

请提供具体的数字参考，并注明数据来源和时效性。"""
            
            messages = [
                {"role": "system", "content": "你是薪资分析专家，请提供准确的薪资市场分析。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.5)
            
            # Extract salary range and factors
            salary_range = "面议"
            factors = []
            
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if any(keyword in line for keyword in ['万', 'k', 'K', '元', '薪资', '工资']):
                    if '范围' in line or '区间' in line:
                        salary_range = line
                elif line and (line.startswith(('1.', '2.', '3.')) or '因素' in line):
                    factors.append(line)
            
            return {
                "range": salary_range,
                "factors": factors[:5],
                "analysis": response
            }
            
        except Exception as e:
            logger.error(f"Error getting salary analysis: {e}")
            return {
                "range": f"获取薪资分析时出现错误：{str(e)}",
                "factors": [],
                "analysis": ""
            }
    
    def get_job_market_analysis(self, position: str, location: str = "") -> Dict:
        """
        Get job market analysis for a specific position
        
        Args:
            position: Job position
            location: Location (optional)
            
        Returns:
            Dictionary containing job market analysis
        """
        try:
            if not llm_client:
                return {"demand": "AI服务暂时不可用", "competition": "", "outlook": ""}
            
            location_text = f"在{location}" if location else ""
            
            prompt = f"""请分析{position}岗位{location_text}的就业市场情况，包括：

1. 市场需求量和招聘活跃度
2. 竞争激烈程度
3. 主要招聘公司类型
4. 岗位要求变化趋势
5. 就业前景和建议

请提供客观、实用的市场分析。"""
            
            messages = [
                {"role": "system", "content": "你是就业市场分析专家，请提供准确的就业市场分析。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.6)
            
            return {
                "demand": "中等" if "中等" in response else "较高" if "高" in response else "一般",
                "competition": "激烈" if "激烈" in response else "适中",
                "outlook": response,
                "analysis": response
            }
            
        except Exception as e:
            logger.error(f"Error getting job market analysis: {e}")
            return {
                "demand": f"获取就业市场分析时出现错误：{str(e)}",
                "competition": "",
                "outlook": ""
            }
    
    def get_skill_requirements(self, position: str, industry: str = "") -> List[str]:
        """
        Get skill requirements for a specific position
        
        Args:
            position: Job position
            industry: Industry context (optional)
            
        Returns:
            List of required skills
        """
        try:
            if not llm_client:
                return ["AI服务暂时不可用"]
            
            industry_text = f"在{industry}行业" if industry else ""
            
            prompt = f"""请列出{position}岗位{industry_text}的核心技能要求，包括：

1. 必备技术技能
2. 软技能要求
3. 工具和平台使用
4. 行业知识要求
5. 证书或资质要求

请按重要性排序，并简要说明每项技能的重要程度。"""
            
            messages = [
                {"role": "system", "content": "你是技能需求分析专家，请提供准确的岗位技能要求。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.5)
            
            # Extract skills from response
            skills = []
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and (line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•')) or 
                           any(keyword in line for keyword in ['技能', '能力', '要求', '掌握'])):
                    skills.append(line)
            
            return skills[:10]  # Top 10 skills
            
        except Exception as e:
            logger.error(f"Error getting skill requirements: {e}")
            return [f"获取技能要求时出现错误：{str(e)}"]

# Global industry expert agent instance
industry_agent = IndustryExpertAgent()
