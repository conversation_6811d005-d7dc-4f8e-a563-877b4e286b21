"""
OpenAI LLM Client for the Career Guidance AI Assistant
"""
import openai
import json
import logging
from typing import Dict, List, Optional, Tuple
from config import OPENAI_API_KEY, OPENAI_API_BASE, OPENAI_MODEL, OPENAI_TEMPERATURE, OPENAI_MAX_TOKENS
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


client = OpenAI(api_key=OPENAI_API_KEY, base_url=OPENAI_API_BASE)

class LLMClient:
    """OpenAI API client wrapper"""
    
    def __init__(self):
        if not OPENAI_API_KEY:
            raise ValueError("OpenAI API key not found. Please set OPENAI_API_KEY in your environment.")
        openai.api_key = OPENAI_API_KEY
    
    def chat_completion(self, messages: List[Dict[str, str]], 
                       temperature: float = OPENAI_TEMPERATURE,
                       max_tokens: int = OPENAI_MAX_TOKENS) -> str:
        """
        Send a chat completion request to OpenAI
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            
        Returns:
            Response content as string
        """
        try:
            response = client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return f"抱歉，AI服务暂时不可用。错误信息：{str(e)}"
    
    def extract_user_profile_info(self, user_input: str, existing_profile: Dict) -> Dict:
        """
        Extract new user profile information from user input
        
        Args:
            user_input: User's message
            existing_profile: Current user profile
            
        Returns:
            Dictionary with extracted new information
        """
        system_prompt = """你是一个用户画像解析助手。
请从以下用户对话文本中，提取与用户个人信息、求职意向、已有技能、项目/实习经历或兴趣偏好等相关的新增信息，并以 JSON 格式输出。
保证输出的 JSON 仅包含新增或更新的字段，例如：
{"技能与兴趣": {"已有技能": ["Python", "React"]}, "求职意向": {"目标岗位": "后端开发"}}

如果没有发现新的用户信息，请返回空的 JSON 对象：{}"""
        
        user_prompt = f"用户对话：{user_input}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self.chat_completion(messages, temperature=0.3)
            # Try to extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                return {}
        except Exception as e:
            logger.error(f"Error extracting user profile info: {e}")
            return {}

# Global LLM client instance
llm_client = LLMClient() if OPENAI_API_KEY else None
