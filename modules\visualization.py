"""
Visualization Module for Career Guidance AI Assistant
"""
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Optional
import os
import logging
from config import RADAR_CHART_SKILLS

logger = logging.getLogger(__name__)

# Set matplotlib to use a non-interactive backend
plt.switch_backend('Agg')

# Configure matplotlib for Chinese font support
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class VisualizationManager:
    """Manages chart generation and visualization"""
    
    def __init__(self):
        self.charts_dir = "charts"
        if not os.path.exists(self.charts_dir):
            os.makedirs(self.charts_dir)
    
    def create_skill_radar_chart(self, user_skills: List[str], target_position: str) -> Optional[str]:
        """
        Create a radar chart comparing user skills with position requirements
        
        Args:
            user_skills: List of user's current skills
            target_position: Target position name
            
        Returns:
            Path to saved chart image or None if failed
        """
        try:
            # Get position requirements
            position_key = self._map_position_to_key(target_position)
            if position_key not in RADAR_CHART_SKILLS:
                logger.warning(f"No skill requirements found for position: {target_position}")
                return None
            
            skill_requirements = RADAR_CHART_SKILLS[position_key]
            
            # Calculate user skill scores
            user_scores = {}
            for skill, requirement in skill_requirements.items():
                # Simple matching - can be enhanced with fuzzy matching
                if any(user_skill.lower() in skill.lower() or skill.lower() in user_skill.lower() 
                      for user_skill in user_skills):
                    user_scores[skill] = min(requirement, 4)  # Cap at 4 to show room for improvement
                else:
                    user_scores[skill] = 1  # Basic score if skill not found
            
            # Create radar chart
            labels = list(skill_requirements.keys())
            num_vars = len(labels)
            
            # Compute angles for each skill
            angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()
            angles += angles[:1]  # Complete the circle
            
            # Values for position requirements and user skills
            req_values = [skill_requirements[label] for label in labels]
            req_values += req_values[:1]
            
            user_values = [user_scores[label] for label in labels]
            user_values += user_values[:1]
            
            # Create the plot
            fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
            
            # Plot position requirements
            ax.plot(angles, req_values, 'o-', linewidth=2, label='岗位要求', color='blue')
            ax.fill(angles, req_values, alpha=0.25, color='blue')
            
            # Plot user skills
            ax.plot(angles, user_values, 'o-', linewidth=2, label='当前水平', color='red')
            ax.fill(angles, user_values, alpha=0.25, color='red')
            
            # Customize the chart
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(labels, fontsize=10)
            ax.set_ylim(0, 5)
            ax.set_yticks([1, 2, 3, 4, 5])
            ax.set_yticklabels(['1', '2', '3', '4', '5'], fontsize=8)
            ax.grid(True)
            
            plt.title(f'技能雷达图 - {target_position}', size=16, fontweight='bold', pad=20)
            plt.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
            
            # Save the chart
            chart_path = os.path.join(self.charts_dir, 'skill_radar.png')
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Skill radar chart saved to {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error creating radar chart: {e}")
            return None
    
    def create_learning_timeline(self, learning_plan: Dict) -> Optional[str]:
        """
        Create a timeline visualization for learning plan
        
        Args:
            learning_plan: Dictionary containing learning plan data
            
        Returns:
            Path to saved chart image or None if failed
        """
        try:
            if not learning_plan:
                return None
            
            # Extract timeline data
            timeline_data = []
            for period, goals in learning_plan.items():
                if isinstance(goals, list):
                    for i, goal in enumerate(goals):
                        timeline_data.append({
                            'Period': period,
                            'Goal': goal,
                            'Order': i
                        })
            
            if not timeline_data:
                return None
            
            # Create DataFrame
            df = pd.DataFrame(timeline_data)
            
            # Create a simple timeline chart
            fig, ax = plt.subplots(figsize=(12, 6))
            
            periods = df['Period'].unique()
            y_positions = range(len(periods))
            
            # Plot timeline
            for i, period in enumerate(periods):
                period_goals = df[df['Period'] == period]['Goal'].tolist()
                ax.barh(i, 1, height=0.6, alpha=0.7, 
                       label=f"{period}: {len(period_goals)} 个目标")
                
                # Add goal text
                goal_text = '\n'.join(period_goals[:2])  # Show first 2 goals
                if len(period_goals) > 2:
                    goal_text += f'\n... 等{len(period_goals)}个目标'
                
                ax.text(0.5, i, goal_text, ha='center', va='center', 
                       fontsize=8, wrap=True)
            
            ax.set_yticks(y_positions)
            ax.set_yticklabels(periods)
            ax.set_xlabel('学习进度')
            ax.set_title('个人学习计划时间线', fontsize=14, fontweight='bold')
            ax.set_xlim(0, 1)
            
            # Save the chart
            chart_path = os.path.join(self.charts_dir, 'learning_timeline.png')
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Learning timeline chart saved to {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error creating learning timeline: {e}")
            return None
    
    def _map_position_to_key(self, position: str) -> str:
        """Map position name to skill requirements key"""
        position_lower = position.lower()
        
        if any(keyword in position_lower for keyword in ['前端', 'frontend', 'react', 'vue', 'javascript']):
            return '前端开发'
        elif any(keyword in position_lower for keyword in ['后端', 'backend', 'java', 'python', 'node']):
            return '后端开发'
        elif any(keyword in position_lower for keyword in ['数据', 'data', '分析', 'analyst']):
            return '数据分析'
        else:
            return '前端开发'  # Default fallback
    
    def create_industry_overview_chart(self, industry_data: Dict) -> Optional[str]:
        """
        Create a chart showing industry overview data
        
        Args:
            industry_data: Dictionary containing industry information
            
        Returns:
            Path to saved chart image or None if failed
        """
        try:
            if not industry_data or '主要岗位' not in industry_data:
                return None
            
            positions = industry_data.get('主要岗位', [])
            if not positions:
                return None
            
            # Create a simple bar chart of positions
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Mock data for demonstration - in real app, this could come from API
            position_counts = [np.random.randint(50, 200) for _ in positions]
            
            bars = ax.bar(positions, position_counts, color='skyblue', alpha=0.7)
            
            # Add value labels on bars
            for bar, count in zip(bars, position_counts):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                       f'{count}', ha='center', va='bottom')
            
            ax.set_ylabel('招聘需求量')
            ax.set_title('行业主要岗位需求概览', fontsize=14, fontweight='bold')
            plt.xticks(rotation=45, ha='right')
            
            # Save the chart
            chart_path = os.path.join(self.charts_dir, 'industry_overview.png')
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Industry overview chart saved to {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error creating industry overview chart: {e}")
            return None

# Global visualization manager instance
viz_manager = VisualizationManager()
