"""
Visualization Module for Career Guidance AI Assistant
"""
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Optional
import os
import logging
from config import RADAR_CHART_SKILLS

logger = logging.getLogger(__name__)

# Set matplotlib to use a non-interactive backend
plt.switch_backend('Agg')

# Configure matplotlib for Chinese font support
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class VisualizationManager:
    """Manages chart generation and visualization"""
    
    def __init__(self):
        self.charts_dir = "charts"
        if not os.path.exists(self.charts_dir):
            os.makedirs(self.charts_dir)
    
    def create_skill_radar_chart(self, user_skills: List[str], target_position: str) -> Optional[str]:
        """
        Create a radar chart comparing user skills with position requirements

        Args:
            user_skills: List of user's current skills
            target_position: Target position name

        Returns:
            Path to saved chart image or None if failed
        """
        try:
            # Generate dynamic skill requirements based on position
            skill_requirements = self._generate_skill_requirements(target_position, user_skills)
            if not skill_requirements:
                logger.warning(f"Could not generate skill requirements for position: {target_position}")
                return None
            
            # Calculate user skill scores
            user_scores = {}
            for skill, requirement in skill_requirements.items():
                # Simple matching - can be enhanced with fuzzy matching
                if any(user_skill.lower() in skill.lower() or skill.lower() in user_skill.lower() 
                      for user_skill in user_skills):
                    user_scores[skill] = min(requirement, 4)  # Cap at 4 to show room for improvement
                else:
                    user_scores[skill] = 1  # Basic score if skill not found
            
            # Create radar chart
            labels = list(skill_requirements.keys())
            num_vars = len(labels)
            
            # Compute angles for each skill
            angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()
            angles += angles[:1]  # Complete the circle
            
            # Values for position requirements and user skills
            req_values = [skill_requirements[label] for label in labels]
            req_values += req_values[:1]
            
            user_values = [user_scores[label] for label in labels]
            user_values += user_values[:1]
            
            # Create the plot
            fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
            
            # Plot position requirements
            ax.plot(angles, req_values, 'o-', linewidth=2, label='岗位要求', color='blue')
            ax.fill(angles, req_values, alpha=0.25, color='blue')
            
            # Plot user skills
            ax.plot(angles, user_values, 'o-', linewidth=2, label='当前水平', color='red')
            ax.fill(angles, user_values, alpha=0.25, color='red')
            
            # Customize the chart
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(labels, fontsize=10)
            ax.set_ylim(0, 5)
            ax.set_yticks([1, 2, 3, 4, 5])
            ax.set_yticklabels(['1', '2', '3', '4', '5'], fontsize=8)
            ax.grid(True)
            
            plt.title(f'技能雷达图 - {target_position}', size=16, fontweight='bold', pad=20)
            plt.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
            
            # Save the chart
            chart_path = os.path.join(self.charts_dir, 'skill_radar.png')
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Skill radar chart saved to {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error creating radar chart: {e}")
            return None
    
    def create_learning_timeline(self, learning_plan: Dict) -> Optional[str]:
        """
        Create a timeline visualization for learning plan
        
        Args:
            learning_plan: Dictionary containing learning plan data
            
        Returns:
            Path to saved chart image or None if failed
        """
        try:
            if not learning_plan:
                return None
            
            # Extract timeline data
            timeline_data = []
            for period, goals in learning_plan.items():
                if isinstance(goals, list):
                    for i, goal in enumerate(goals):
                        timeline_data.append({
                            'Period': period,
                            'Goal': goal,
                            'Order': i
                        })
            
            if not timeline_data:
                return None
            
            # Create DataFrame
            df = pd.DataFrame(timeline_data)
            
            # Create a simple timeline chart
            fig, ax = plt.subplots(figsize=(12, 6))
            
            periods = df['Period'].unique()
            y_positions = range(len(periods))
            
            # Plot timeline
            for i, period in enumerate(periods):
                period_goals = df[df['Period'] == period]['Goal'].tolist()
                ax.barh(i, 1, height=0.6, alpha=0.7, 
                       label=f"{period}: {len(period_goals)} 个目标")
                
                # Add goal text
                goal_text = '\n'.join(period_goals[:2])  # Show first 2 goals
                if len(period_goals) > 2:
                    goal_text += f'\n... 等{len(period_goals)}个目标'
                
                ax.text(0.5, i, goal_text, ha='center', va='center', 
                       fontsize=8, wrap=True)
            
            ax.set_yticks(y_positions)
            ax.set_yticklabels(periods)
            ax.set_xlabel('学习进度')
            ax.set_title('个人学习计划时间线', fontsize=14, fontweight='bold')
            ax.set_xlim(0, 1)
            
            # Save the chart
            chart_path = os.path.join(self.charts_dir, 'learning_timeline.png')
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Learning timeline chart saved to {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error creating learning timeline: {e}")
            return None
    
    def _generate_skill_requirements(self, target_position: str, user_skills: List[str]) -> Dict[str, int]:
        """
        Generate dynamic skill requirements based on position and user skills

        Args:
            target_position: Target position name
            user_skills: User's current skills

        Returns:
            Dictionary of skill requirements with importance scores
        """
        try:
            from utils.llm_client import llm_client

            if not llm_client:
                # Fallback to static mapping if no LLM available
                return self._get_static_skill_requirements(target_position)

            # Use LLM to generate dynamic skill requirements
            prompt = f"""请为"{target_position}"岗位生成6-8个核心技能维度，并为每个维度评分(1-5分，5分最重要)。

用户当前技能：{', '.join(user_skills) if user_skills else '无'}

请以JSON格式返回，例如：
{{
  "编程语言": 5,
  "框架技术": 4,
  "数据库": 4,
  "系统设计": 3,
  "项目管理": 3,
  "沟通协作": 3
}}

要求：
1. 技能维度要具体且相关
2. 考虑用户已有技能，包含相关维度
3. 评分要合理反映该岗位的重要性
4. 维度名称要简洁明了"""

            messages = [
                {"role": "system", "content": "你是技能评估专家，请为不同岗位生成准确的技能要求维度。"},
                {"role": "user", "content": prompt}
            ]

            response = llm_client.chat_completion(messages, temperature=0.3)

            # Extract JSON from response
            import json
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                skill_requirements = json.loads(json_str)

                # Validate and clean the requirements
                cleaned_requirements = {}
                for skill, score in skill_requirements.items():
                    if isinstance(score, (int, float)) and 1 <= score <= 5:
                        cleaned_requirements[skill] = int(score)

                if len(cleaned_requirements) >= 4:  # At least 4 dimensions
                    logger.info(f"Generated dynamic skill requirements for {target_position}")
                    return cleaned_requirements

            # Fallback if LLM response is invalid
            logger.warning("LLM response invalid, using static requirements")
            return self._get_static_skill_requirements(target_position)

        except Exception as e:
            logger.error(f"Error generating skill requirements: {e}")
            return self._get_static_skill_requirements(target_position)

    def _get_static_skill_requirements(self, target_position: str) -> Dict[str, int]:
        """Get static skill requirements as fallback"""
        position_lower = target_position.lower()

        # Enhanced static mappings with more position types
        if any(keyword in position_lower for keyword in ['前端', 'frontend', 'react', 'vue', 'javascript', 'web前端']):
            return {
                "HTML/CSS": 4,
                "JavaScript": 5,
                "前端框架": 4,
                "工程化工具": 3,
                "UI/UX": 3,
                "沟通协作": 3
            }
        elif any(keyword in position_lower for keyword in ['后端', 'backend', 'java', 'python', 'node', 'server']):
            return {
                "编程语言": 5,
                "数据库": 4,
                "系统设计": 4,
                "API设计": 4,
                "性能优化": 3,
                "团队协作": 3
            }
        elif any(keyword in position_lower for keyword in ['全栈', 'fullstack', 'full-stack']):
            return {
                "前端技术": 4,
                "后端技术": 4,
                "数据库": 4,
                "系统架构": 3,
                "项目管理": 3,
                "技术选型": 3
            }
        elif any(keyword in position_lower for keyword in ['数据', 'data', '分析', 'analyst', '算法']):
            return {
                "编程语言": 5,
                "数据处理": 4,
                "统计分析": 4,
                "机器学习": 3,
                "数据可视化": 4,
                "业务理解": 4
            }
        elif any(keyword in position_lower for keyword in ['产品', 'product', '经理', 'manager']):
            return {
                "需求分析": 5,
                "产品设计": 4,
                "用户研究": 4,
                "项目管理": 4,
                "沟通协调": 5,
                "数据分析": 3
            }
        elif any(keyword in position_lower for keyword in ['运营', 'operation', '市场', 'marketing']):
            return {
                "内容策划": 4,
                "数据分析": 4,
                "用户运营": 5,
                "渠道管理": 3,
                "创意思维": 4,
                "沟通能力": 4
            }
        elif any(keyword in position_lower for keyword in ['设计', 'design', 'ui', 'ux']):
            return {
                "设计软件": 5,
                "用户体验": 5,
                "视觉设计": 4,
                "交互设计": 4,
                "创意思维": 4,
                "沟通协作": 3
            }
        elif any(keyword in position_lower for keyword in ['测试', 'test', 'qa', '质量']):
            return {
                "测试方法": 5,
                "自动化测试": 4,
                "编程能力": 3,
                "质量管理": 4,
                "细致耐心": 4,
                "沟通协调": 3
            }
        else:
            # Generic professional skills for unknown positions
            return {
                "专业技能": 5,
                "学习能力": 4,
                "沟通协作": 4,
                "问题解决": 4,
                "项目管理": 3,
                "创新思维": 3
            }
    
    def create_industry_overview_chart(self, industry_data: Dict) -> Optional[str]:
        """
        Create a chart showing industry overview data
        
        Args:
            industry_data: Dictionary containing industry information
            
        Returns:
            Path to saved chart image or None if failed
        """
        try:
            if not industry_data or '主要岗位' not in industry_data:
                return None
            
            positions = industry_data.get('主要岗位', [])
            if not positions:
                return None
            
            # Create a simple bar chart of positions
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Mock data for demonstration - in real app, this could come from API
            position_counts = [np.random.randint(50, 200) for _ in positions]
            
            bars = ax.bar(positions, position_counts, color='skyblue', alpha=0.7)
            
            # Add value labels on bars
            for bar, count in zip(bars, position_counts):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                       f'{count}', ha='center', va='bottom')
            
            ax.set_ylabel('招聘需求量')
            ax.set_title('行业主要岗位需求概览', fontsize=14, fontweight='bold')
            plt.xticks(rotation=45, ha='right')
            
            # Save the chart
            chart_path = os.path.join(self.charts_dir, 'industry_overview.png')
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Industry overview chart saved to {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error creating industry overview chart: {e}")
            return None

# Global visualization manager instance
viz_manager = VisualizationManager()
