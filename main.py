"""
Main Gradio Application for Career Guidance AI Assistant
"""
import gradio as gr
import os
import logging
from typing import List, Tuple, Optional
from config import APP_TITLE, APP_DESCRIPTION
from modules.user_profile import profile_manager
from agents.chat_agent import chat_agent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CareerGuidanceApp:
    """Main application class for the Career Guidance AI Assistant"""
    
    def __init__(self):
        self.current_chart = None
        
    def create_interface(self):
        """Create and configure the Gradio interface"""
        
        with gr.Blocks(title=APP_TITLE, theme=gr.themes.Soft()) as app:
            # Header
            gr.Markdown(f"# {APP_TITLE}")
            gr.Markdown(APP_DESCRIPTION)
            
            # Check if profile exists
            profile_complete = profile_manager.is_profile_complete()
            
            with gr.Row():
                with gr.Column(scale=2):
                    # Profile Setup Section (shown if profile incomplete)
                    with gr.<PERSON>(visible=not profile_complete) as profile_setup:
                        gr.Markdown("## 👤 个人信息设置")
                        gr.Markdown("请先填写基本信息，以便为您提供个性化的求职建议：")
                        
                        with gr.<PERSON>():
                            name_input = gr.Textbox(label="姓名（可选）", placeholder="请输入您的姓名")
                            age_input = gr.Number(label="年龄", value=22, minimum=16, maximum=65)
                        
                        with gr.Row():
                            education_input = gr.Dropdown(
                                label="学历",
                                choices=["高中", "大专", "本科", "硕士", "博士"],
                                value="本科"
                            )
                            major_input = gr.Textbox(label="专业", placeholder="如：计算机科学与技术")
                        
                        with gr.Row():
                            location_input = gr.Textbox(label="所在城市", placeholder="如：北京")
                            graduation_input = gr.Textbox(label="毕业时间", placeholder="如：2025年6月")
                        
                        with gr.Row():
                            target_industry_input = gr.Dropdown(
                                label="目标行业",
                                choices=["互联网/IT", "金融", "教育", "医疗", "制造业", "咨询", "其他"],
                                value="互联网/IT"
                            )
                            target_position_input = gr.Textbox(label="目标岗位", placeholder="如：前端开发工程师")
                        
                        skills_input = gr.Textbox(
                            label="已有技能",
                            placeholder="请用逗号分隔，如：Python, JavaScript, HTML/CSS",
                            lines=2
                        )
                        
                        projects_input = gr.Textbox(
                            label="项目经历",
                            placeholder="请简述您的项目经历，用逗号分隔",
                            lines=2
                        )
                        
                        interests_input = gr.Textbox(
                            label="兴趣方向",
                            placeholder="如：Web开发, 数据分析, 机器学习",
                            lines=2
                        )
                        
                        job_status_input = gr.Dropdown(
                            label="求职状态",
                            choices=["在校学生", "应届毕业生", "在职求职", "待业求职", "其他"],
                            value="在校学生"
                        )
                        
                        setup_btn = gr.Button("完成设置", variant="primary", size="lg")
                    
                    # Chat Interface (shown after profile setup)
                    with gr.Group(visible=profile_complete) as chat_interface:
                        gr.Markdown("## 💬 智能求职助手")
                        
                        chatbot = gr.Chatbot(
                            height=400,
                            placeholder="您好！我是您的求职助手，可以为您提供职业规划、技能提升和行业咨询服务。请告诉我您想了解什么？"
                        )
                        
                        with gr.Row():
                            msg_input = gr.Textbox(
                                label="",
                                placeholder="请输入您的问题...",
                                scale=4
                            )
                            send_btn = gr.Button("发送", variant="primary", scale=1)
                        
                        # Quick action buttons
                        with gr.Row():
                            career_btn = gr.Button("职业规划", size="sm")
                            skill_btn = gr.Button("技能提升", size="sm") 
                            industry_btn = gr.Button("行业咨询", size="sm")
                            clear_btn = gr.Button("清空对话", size="sm")
                
                with gr.Column(scale=1):
                    # Sidebar with user info and visualizations
                    with gr.Group():
                        gr.Markdown("## 📊 个人概览")
                        
                        # User stats
                        stats_display = gr.JSON(
                            label="用户统计",
                            value=chat_agent.get_user_stats() if profile_complete else {}
                        )
                        
                        # Chart display
                        chart_display = gr.Image(
                            label="可视化图表",
                            visible=False
                        )
                        
                        # Profile management
                        with gr.Accordion("个人信息管理", open=False):
                            view_profile_btn = gr.Button("查看完整档案", size="sm")
                            edit_profile_btn = gr.Button("编辑个人信息", size="sm")
                            export_btn = gr.Button("导出对话记录", size="sm")

            # Profile editing modal (initially hidden)
            with gr.Group(visible=False) as profile_edit_modal:
                gr.Markdown("## ✏️ 编辑个人信息")

                with gr.Row():
                    edit_name = gr.Textbox(label="姓名", placeholder="请输入您的姓名")
                    edit_age = gr.Number(label="年龄", value=22, minimum=16, maximum=65)

                with gr.Row():
                    edit_education = gr.Dropdown(
                        label="学历",
                        choices=["高中", "大专", "本科", "硕士", "博士"],
                        value="本科"
                    )
                    edit_major = gr.Textbox(label="专业", placeholder="如：计算机科学与技术")

                with gr.Row():
                    edit_location = gr.Textbox(label="所在城市", placeholder="如：北京")
                    edit_graduation = gr.Textbox(label="毕业时间", placeholder="如：2025年6月")

                with gr.Row():
                    edit_target_industry = gr.Dropdown(
                        label="目标行业",
                        choices=["互联网/IT", "金融", "教育", "医疗", "制造业", "咨询", "其他"],
                        value="互联网/IT"
                    )
                    edit_target_position = gr.Textbox(label="目标岗位", placeholder="如：前端开发工程师")

                edit_skills = gr.Textbox(
                    label="已有技能",
                    placeholder="请用逗号分隔，如：Python, JavaScript, HTML/CSS",
                    lines=2
                )

                edit_projects = gr.Textbox(
                    label="项目经历",
                    placeholder="请简述您的项目经历，用逗号分隔",
                    lines=2
                )

                edit_interests = gr.Textbox(
                    label="兴趣方向",
                    placeholder="如：Web开发, 数据分析, 机器学习",
                    lines=2
                )

                edit_job_status = gr.Dropdown(
                    label="求职状态",
                    choices=["在校学生", "应届毕业生", "在职求职", "待业求职", "其他"],
                    value="在校学生"
                )

                with gr.Row():
                    save_edit_btn = gr.Button("保存修改", variant="primary", size="lg")
                    cancel_edit_btn = gr.Button("取消", size="lg")

                # Status message for edit operations
                edit_status = gr.Textbox(label="状态", visible=False)

            # Export modal (initially hidden)
            with gr.Group(visible=False) as export_modal:
                gr.Markdown("## 📄 导出对话记录")
                export_content = gr.Textbox(
                    label="对话记录",
                    lines=20,
                    max_lines=30,
                    show_copy_button=True
                )
                close_export_btn = gr.Button("关闭", size="lg")
            
            # Event handlers
            def setup_profile(*args):
                """Handle profile setup"""
                try:
                    form_data = {
                        "name": args[0],
                        "age": int(args[1]) if args[1] else 0,
                        "education": args[2],
                        "major": args[3],
                        "location": args[4],
                        "graduation": args[5],
                        "target_industry": args[6],
                        "target_position": args[7],
                        "skills": [s.strip() for s in args[8].split(",") if s.strip()] if args[8] else [],
                        "projects": [p.strip() for p in args[9].split(",") if p.strip()] if args[9] else [],
                        "interests": [i.strip() for i in args[10].split(",") if i.strip()] if args[10] else [],
                        "job_status": args[11]
                    }

                    success = profile_manager.initialize_profile(form_data)
                    if success:
                        return (
                            gr.update(visible=False),  # Hide profile setup
                            gr.update(visible=True),   # Show chat interface
                            chat_agent.get_user_stats()  # Update stats
                        )
                    else:
                        gr.Warning("设置失败，请重试")
                        return gr.update(), gr.update(), {}

                except Exception as e:
                    logger.error(f"Error in profile setup: {e}")
                    gr.Error(f"设置过程中出现错误：{str(e)}")
                    return gr.update(), gr.update(), {}

            def chat_response(message, history):
                """Handle chat message"""
                try:
                    if not message.strip():
                        return history, "", None, chat_agent.get_user_stats()

                    # Process message
                    response_text, chart_path, metadata = chat_agent.process_message(message)

                    # Update chat history
                    history.append([message, response_text])

                    # Update chart if generated
                    chart_update = gr.update()
                    if chart_path and os.path.exists(chart_path):
                        chart_update = gr.update(value=chart_path, visible=True)

                    return history, "", chart_update, chat_agent.get_user_stats()

                except Exception as e:
                    logger.error(f"Error in chat response: {e}")
                    error_msg = f"处理消息时出现错误：{str(e)}"
                    history.append([message, error_msg])
                    return history, "", gr.update(), {}

            def quick_action(action_type):
                """Handle quick action buttons"""
                prompts = {
                    "career": "请根据我的个人情况，为我制定一个详细的职业发展规划。",
                    "skill": "我想提升自己的技能水平，请给我一个学习计划。",
                    "industry": "请分析一下我目标行业的发展前景和就业情况。"
                }
                return prompts.get(action_type, "")

            def clear_chat():
                """Clear chat history"""
                chat_agent.clear_conversation_history()
                return [], chat_agent.get_user_stats()

            def view_profile():
                """View complete user profile"""
                profile = profile_manager.get_profile()
                return profile

            def edit_profile():
                """Show profile editing form with current values"""
                try:
                    profile = profile_manager.get_profile()
                    basic_info = profile.get("基本信息", {})
                    job_intent = profile.get("求职意向", {})
                    skills_info = profile.get("技能与兴趣", {})

                    return (
                        gr.update(visible=True),  # Show edit modal
                        gr.update(visible=False), # Hide chat interface
                        basic_info.get("姓名", ""),
                        basic_info.get("年龄", 22),
                        basic_info.get("学历", "本科"),
                        basic_info.get("专业", ""),
                        basic_info.get("所在地", ""),
                        basic_info.get("毕业时间", ""),
                        job_intent.get("目标行业", "互联网/IT"),
                        job_intent.get("目标岗位", ""),
                        ", ".join(skills_info.get("已有技能", [])),
                        ", ".join(skills_info.get("项目经历", [])),
                        ", ".join(skills_info.get("兴趣方向", [])),
                        job_intent.get("求职状态", "在校学生")
                    )
                except Exception as e:
                    logger.error(f"Error loading profile for editing: {e}")
                    return (gr.update(), gr.update()) + ("",) * 12

            def save_profile_edit(*args):
                """Save profile edits"""
                try:
                    form_data = {
                        "name": args[0],
                        "age": int(args[1]) if args[1] else 0,
                        "education": args[2],
                        "major": args[3],
                        "location": args[4],
                        "graduation": args[5],
                        "target_industry": args[6],
                        "target_position": args[7],
                        "skills": [s.strip() for s in args[8].split(",") if s.strip()] if args[8] else [],
                        "projects": [p.strip() for p in args[9].split(",") if p.strip()] if args[9] else [],
                        "interests": [i.strip() for i in args[10].split(",") if i.strip()] if args[10] else [],
                        "job_status": args[11]
                    }

                    # Use update_profile with replace_lists=True for editing
                    success = profile_manager.update_profile({
                        "基本信息": {
                            "姓名": form_data["name"],
                            "年龄": form_data["age"],
                            "学历": form_data["education"],
                            "专业": form_data["major"],
                            "毕业时间": form_data["graduation"],
                            "所在地": form_data["location"]
                        },
                        "求职意向": {
                            "目标行业": form_data["target_industry"],
                            "目标岗位": form_data["target_position"],
                            "期望薪资": form_data.get("expected_salary", ""),
                            "求职状态": form_data["job_status"]
                        },
                        "技能与兴趣": {
                            "已有技能": form_data["skills"],
                            "项目经历": form_data["projects"],
                            "兴趣方向": form_data["interests"]
                        }
                    }, replace_lists=True)  # Allow deletion of skills/projects

                    if success:
                        # Generate updated radar chart
                        chart_path = None
                        try:
                            from modules.visualization import viz_manager
                            user_skills = form_data["skills"]
                            target_position = form_data["target_position"]
                            if user_skills and target_position:
                                chart_path = viz_manager.create_skill_radar_chart(user_skills, target_position)
                        except Exception as e:
                            logger.warning(f"Failed to generate radar chart: {e}")

                        chart_update = gr.update()
                        if chart_path and os.path.exists(chart_path):
                            chart_update = gr.update(value=chart_path, visible=True)

                        return (
                            gr.update(visible=False),  # Hide edit modal
                            gr.update(visible=True),   # Show chat interface
                            chat_agent.get_user_stats(),  # Update stats
                            "✅ 个人信息已更新成功！",
                            chart_update  # Update chart
                        )
                    else:
                        return (
                            gr.update(),
                            gr.update(),
                            {},
                            "❌ 保存失败，请重试",
                            gr.update()  # Chart update
                        )

                except Exception as e:
                    logger.error(f"Error saving profile edit: {e}")
                    return (
                        gr.update(),
                        gr.update(),
                        {},
                        f"❌ 保存过程中出现错误：{str(e)}",
                        gr.update()  # Chart update
                    )

            def cancel_profile_edit():
                """Cancel profile editing"""
                return (
                    gr.update(visible=False),  # Hide edit modal
                    gr.update(visible=True)    # Show chat interface
                )

            def export_conversations():
                """Export conversation history"""
                try:
                    profile = profile_manager.get_profile()
                    history = profile.get("行为数据", {}).get("历史对话", [])

                    if not history:
                        return "📝 暂无对话记录可导出"

                    export_text = "# 求职助手对话记录\n\n"
                    for i, conv in enumerate(history, 1):
                        if isinstance(conv, dict):
                            timestamp = conv.get("timestamp", "未知时间")
                            user_msg = conv.get("user", "")
                            assistant_msg = conv.get("assistant", "")
                            export_text += f"## 对话 {i} ({timestamp})\n\n"
                            export_text += f"**用户**: {user_msg}\n\n"
                            export_text += f"**助手**: {assistant_msg}\n\n---\n\n"

                    return export_text

                except Exception as e:
                    logger.error(f"Error exporting conversations: {e}")
                    return f"❌ 导出失败：{str(e)}"

            # Wire up event handlers
            setup_btn.click(
                setup_profile,
                inputs=[
                    name_input, age_input, education_input, major_input,
                    location_input, graduation_input, target_industry_input,
                    target_position_input, skills_input, projects_input,
                    interests_input, job_status_input
                ],
                outputs=[profile_setup, chat_interface, stats_display]
            )

            send_btn.click(
                chat_response,
                inputs=[msg_input, chatbot],
                outputs=[chatbot, msg_input, chart_display, stats_display]
            )

            msg_input.submit(
                chat_response,
                inputs=[msg_input, chatbot],
                outputs=[chatbot, msg_input, chart_display, stats_display]
            )

            career_btn.click(
                lambda: quick_action("career"),
                outputs=[msg_input]
            )

            skill_btn.click(
                lambda: quick_action("skill"),
                outputs=[msg_input]
            )

            industry_btn.click(
                lambda: quick_action("industry"),
                outputs=[msg_input]
            )

            clear_btn.click(
                clear_chat,
                outputs=[chatbot, stats_display]
            )

            view_profile_btn.click(
                view_profile,
                outputs=[stats_display]
            )

            edit_profile_btn.click(
                edit_profile,
                outputs=[
                    profile_edit_modal, chat_interface,
                    edit_name, edit_age, edit_education, edit_major,
                    edit_location, edit_graduation, edit_target_industry,
                    edit_target_position, edit_skills, edit_projects,
                    edit_interests, edit_job_status
                ]
            )

            save_edit_btn.click(
                save_profile_edit,
                inputs=[
                    edit_name, edit_age, edit_education, edit_major,
                    edit_location, edit_graduation, edit_target_industry,
                    edit_target_position, edit_skills, edit_projects,
                    edit_interests, edit_job_status
                ],
                outputs=[profile_edit_modal, chat_interface, stats_display, edit_status, chart_display]
            )

            cancel_edit_btn.click(
                cancel_profile_edit,
                outputs=[profile_edit_modal, chat_interface]
            )

            export_btn.click(
                lambda: (gr.update(visible=True), export_conversations()),
                outputs=[export_modal, export_content]
            )

            close_export_btn.click(
                lambda: gr.update(visible=False),
                outputs=[export_modal]
            )

        return app

def main():
    """Main entry point"""
    try:
        # Check for OpenAI API key
        if not os.getenv("OPENAI_API_KEY"):
            print("⚠️  警告：未检测到 OPENAI_API_KEY 环境变量")
            print("请设置您的 OpenAI API 密钥：")
            print("export OPENAI_API_KEY='your-api-key-here'")
            print("或在 .env 文件中设置")

        # Create and launch app
        app_instance = CareerGuidanceApp()
        app = app_instance.create_interface()

        print(f"🚀 启动 {APP_TITLE}")
        print("📝 功能包括：")
        print("   • 个人画像管理")
        print("   • 智能职业规划")
        print("   • 技能提升建议")
        print("   • 行业趋势分析")
        print("   • 可视化图表")

        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=True,
            debug=False
        )

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        print(f"❌ 启动失败：{e}")

if __name__ == "__main__":
    main()
