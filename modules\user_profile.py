"""
User Profile Management Module for Career Guidance AI Assistant
"""
import json
import os
import logging
from typing import Dict, Optional
from datetime import datetime
from config import USER_PROFILE_FILE, DEFAULT_USER_PROFILE
from utils.helpers import merge_profile, now_str

logger = logging.getLogger(__name__)

class UserProfileManager:
    """Manages user profile data storage and retrieval"""
    
    def __init__(self, profile_file: str = USER_PROFILE_FILE):
        self.profile_file = profile_file
        self.profile_data = self._load_profile()
    
    def _load_profile(self) -> Dict:
        """Load user profile from file or create default"""
        if os.path.exists(self.profile_file):
            try:
                with open(self.profile_file, 'r', encoding='utf-8') as f:
                    profile = json.load(f)
                logger.info("User profile loaded successfully")
                return profile
            except Exception as e:
                logger.error(f"Error loading profile: {e}")
                return DEFAULT_USER_PROFILE.copy()
        else:
            logger.info("No existing profile found, creating default")
            return DEFAULT_USER_PROFILE.copy()
    
    def save_profile(self) -> bool:
        """Save current profile to file"""
        try:
            self.profile_data["行为数据"]["最近更新时间"] = now_str()
            with open(self.profile_file, 'w', encoding='utf-8') as f:
                json.dump(self.profile_data, f, ensure_ascii=False, indent=2)
            logger.info("User profile saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving profile: {e}")
            return False
    
    def get_profile(self) -> Dict:
        """Get current user profile"""
        return self.profile_data.copy()
    
    def update_profile(self, new_info: Dict, replace_lists: bool = False) -> bool:
        """
        Update profile with new information

        Args:
            new_info: Dictionary containing new profile information
            replace_lists: If True, replace lists entirely instead of merging

        Returns:
            True if update successful, False otherwise
        """
        try:
            self.profile_data = merge_profile(self.profile_data, new_info, replace_lists)
            return self.save_profile()
        except Exception as e:
            logger.error(f"Error updating profile: {e}")
            return False
    
    def initialize_profile(self, form_data: Dict) -> bool:
        """
        Initialize user profile from form data
        
        Args:
            form_data: Dictionary containing form input data
            
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Map form data to profile structure
            profile_update = {
                "基本信息": {
                    "姓名": form_data.get("name", ""),
                    "年龄": form_data.get("age", 0),
                    "学历": form_data.get("education", ""),
                    "专业": form_data.get("major", ""),
                    "毕业时间": form_data.get("graduation", ""),
                    "所在地": form_data.get("location", "")
                },
                "求职意向": {
                    "目标行业": form_data.get("target_industry", ""),
                    "目标岗位": form_data.get("target_position", ""),
                    "期望薪资": form_data.get("expected_salary", ""),
                    "求职状态": form_data.get("job_status", "")
                },
                "技能与兴趣": {
                    "已有技能": form_data.get("skills", []),
                    "项目经历": form_data.get("projects", []),
                    "兴趣方向": form_data.get("interests", [])
                }
            }
            
            return self.update_profile(profile_update)
        except Exception as e:
            logger.error(f"Error initializing profile: {e}")
            return False
    
    def add_conversation(self, user_message: str, assistant_reply: str) -> bool:
        """
        Add a conversation to the history
        
        Args:
            user_message: User's message
            assistant_reply: Assistant's reply
            
        Returns:
            True if successful, False otherwise
        """
        try:
            history = self.profile_data.setdefault("行为数据", {}).setdefault("历史对话", [])
            history.append({
                "timestamp": now_str(),
                "user": user_message,
                "assistant": assistant_reply
            })
            
            # Keep only last 50 conversations to avoid file bloat
            if len(history) > 50:
                self.profile_data["行为数据"]["历史对话"] = history[-50:]
            
            return self.save_profile()
        except Exception as e:
            logger.error(f"Error adding conversation: {e}")
            return False
    
    def is_profile_complete(self) -> bool:
        """Check if user profile has basic required information"""
        basic_info = self.profile_data.get("基本信息", {})
        job_intent = self.profile_data.get("求职意向", {})
        
        required_fields = [
            basic_info.get("学历"),
            job_intent.get("目标行业"),
            job_intent.get("目标岗位")
        ]
        
        return all(field and field.strip() for field in required_fields)
    
    def get_skills_list(self) -> list:
        """Get user's current skills as a list"""
        return self.profile_data.get("技能与兴趣", {}).get("已有技能", [])
    
    def get_target_position(self) -> str:
        """Get user's target position"""
        return self.profile_data.get("求职意向", {}).get("目标岗位", "")

# Global profile manager instance
profile_manager = UserProfileManager()
