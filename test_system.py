"""
Test script for Career Guidance AI Assistant
"""
import os
import sys

def test_imports():
    """Test if all modules can be imported successfully"""
    print("🔍 Testing module imports...")
    
    try:
        from config import APP_TITLE, OPENAI_API_KEY
        print("✅ Config module imported successfully")
        
        from utils.llm_client import llm_client
        print("✅ LLM client imported successfully")
        
        from modules.user_profile import profile_manager
        print("✅ User profile manager imported successfully")
        
        from modules.visualization import viz_manager
        print("✅ Visualization manager imported successfully")
        
        from agents.chat_agent import chat_agent
        from agents.career_planning_agent import career_agent
        from agents.skill_enhancement_agent import skill_agent
        from agents.industry_expert_agent import industry_agent
        print("✅ All agents imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_user_profile():
    """Test user profile management"""
    print("\n👤 Testing user profile management...")
    
    try:
        from modules.user_profile import profile_manager
        
        # Test profile initialization
        test_data = {
            "name": "测试用户",
            "age": 22,
            "education": "本科",
            "major": "计算机科学",
            "location": "北京",
            "target_industry": "互联网/IT",
            "target_position": "前端开发工程师",
            "skills": ["HTML", "CSS", "JavaScript"],
            "projects": ["个人博客项目"],
            "interests": ["Web开发", "用户体验"],
            "job_status": "在校学生"
        }
        
        success = profile_manager.initialize_profile(test_data)
        if success:
            print("✅ Profile initialization successful")
        else:
            print("❌ Profile initialization failed")
            return False
        
        # Test profile retrieval
        profile = profile_manager.get_profile()
        if profile and profile.get("基本信息", {}).get("姓名") == "测试用户":
            print("✅ Profile retrieval successful")
        else:
            print("❌ Profile retrieval failed")
            return False
        
        # Test conversation addition
        success = profile_manager.add_conversation("测试问题", "测试回答")
        if success:
            print("✅ Conversation addition successful")
        else:
            print("❌ Conversation addition failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ User profile test error: {e}")
        return False

def test_intent_classification():
    """Test intent classification"""
    print("\n🎯 Testing intent classification...")
    
    try:
        from utils.helpers import classify_intent
        
        test_cases = [
            ("我想做职业规划", "career"),
            ("如何学习Python", "skill"),
            ("前端开发行业前景如何", "industry"),
            ("你好", "general")
        ]
        
        for text, expected in test_cases:
            result = classify_intent(text)
            if result == expected:
                print(f"✅ '{text}' -> {result}")
            else:
                print(f"❌ '{text}' -> {result} (expected: {expected})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Intent classification test error: {e}")
        return False

def test_visualization():
    """Test visualization generation"""
    print("\n📊 Testing visualization...")
    
    try:
        from modules.visualization import viz_manager
        
        # Test skill radar chart
        user_skills = ["HTML", "CSS", "JavaScript"]
        target_position = "前端开发工程师"
        
        chart_path = viz_manager.create_skill_radar_chart(user_skills, target_position)
        if chart_path and os.path.exists(chart_path):
            print("✅ Skill radar chart generated successfully")
        else:
            print("⚠️  Skill radar chart generation failed (may be due to missing dependencies)")
        
        # Test learning timeline
        learning_plan = {
            "第1-2周": ["学习HTML基础", "练习CSS布局"],
            "第3-4周": ["学习JavaScript", "DOM操作练习"]
        }
        
        timeline_path = viz_manager.create_learning_timeline(learning_plan)
        if timeline_path and os.path.exists(timeline_path):
            print("✅ Learning timeline generated successfully")
        else:
            print("⚠️  Learning timeline generation failed (may be due to missing dependencies)")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualization test error: {e}")
        return False

def test_agents_without_api():
    """Test agents without making actual API calls"""
    print("\n🤖 Testing agents (without API calls)...")
    
    try:
        from agents.career_planning_agent import career_agent
        from agents.skill_enhancement_agent import skill_agent
        from agents.industry_expert_agent import industry_agent
        
        # Test agent initialization
        print("✅ Career planning agent initialized")
        print("✅ Skill enhancement agent initialized")
        print("✅ Industry expert agent initialized")
        
        # Test helper methods that don't require API
        from utils.helpers import parse_skill_and_duration, format_user_profile_for_prompt
        
        skill, duration = parse_skill_and_duration("我想学习Python，给我一个三个月的计划")
        print(f"✅ Skill parsing: {skill}, Duration: {duration}")
        
        test_profile = {
            "基本信息": {"姓名": "测试", "学历": "本科"},
            "求职意向": {"目标岗位": "开发工程师"},
            "技能与兴趣": {"已有技能": ["Python"]}
        }
        
        formatted = format_user_profile_for_prompt(test_profile)
        if "测试" in formatted and "Python" in formatted:
            print("✅ Profile formatting successful")
        else:
            print("❌ Profile formatting failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agents test error: {e}")
        return False

def check_api_key():
    """Check if OpenAI API key is configured"""
    print("\n🔑 Checking API configuration...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print("✅ OpenAI API key found")
        return True
    else:
        print("⚠️  OpenAI API key not found")
        print("   Please set OPENAI_API_KEY environment variable")
        print("   or create a .env file with your API key")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Career Guidance AI Assistant System Tests")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("User Profile Management", test_user_profile),
        ("Intent Classification", test_intent_classification),
        ("Visualization", test_visualization),
        ("Agents (No API)", test_agents_without_api),
        ("API Configuration", check_api_key)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        print("\nTo start the application, run:")
        print("python main.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("Make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
