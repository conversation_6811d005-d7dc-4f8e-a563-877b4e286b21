#!/usr/bin/env python3
"""
Demo script to show profile editing functionality
"""
import os
import sys

def demo_profile_editing():
    """Demonstrate profile editing functionality"""
    print("🔧 Demo: Profile Editing Functionality")
    print("=" * 50)
    
    from modules.user_profile import profile_manager
    
    # Initialize a demo profile first
    print("📝 初始化演示用户档案...")
    demo_data = {
        "name": "李小华",
        "age": 23,
        "education": "本科",
        "major": "软件工程",
        "location": "上海",
        "target_industry": "互联网/IT",
        "target_position": "后端开发工程师",
        "skills": ["Java", "Spring", "MySQL"],
        "projects": ["学生管理系统", "在线购物平台"],
        "interests": ["后端开发", "系统架构"],
        "job_status": "应届毕业生"
    }
    
    success = profile_manager.initialize_profile(demo_data)
    if not success:
        print("❌ 初始化失败")
        return
    
    print("✅ 初始档案创建成功")
    
    # Show original profile
    profile = profile_manager.get_profile()
    print("\n👤 原始档案信息:")
    print(f"姓名: {profile['基本信息']['姓名']}")
    print(f"年龄: {profile['基本信息']['年龄']}")
    print(f"专业: {profile['基本信息']['专业']}")
    print(f"目标岗位: {profile['求职意向']['目标岗位']}")
    print(f"已有技能: {', '.join(profile['技能与兴趣']['已有技能'])}")
    
    # Simulate profile update
    print("\n🔄 模拟档案更新...")
    updated_data = {
        "name": "李小华",
        "age": 23,
        "education": "本科",
        "major": "软件工程",
        "location": "上海",
        "target_industry": "互联网/IT",
        "target_position": "全栈开发工程师",  # 更新目标岗位
        "skills": ["Java", "Spring", "MySQL", "React", "Node.js"],  # 新增技能
        "projects": ["学生管理系统", "在线购物平台", "微服务架构项目"],  # 新增项目
        "interests": ["后端开发", "系统架构", "前端开发"],  # 新增兴趣
        "job_status": "应届毕业生"
    }
    
    success = profile_manager.initialize_profile(updated_data)
    if not success:
        print("❌ 更新失败")
        return
    
    print("✅ 档案更新成功")
    
    # Show updated profile
    updated_profile = profile_manager.get_profile()
    print("\n👤 更新后档案信息:")
    print(f"姓名: {updated_profile['基本信息']['姓名']}")
    print(f"年龄: {updated_profile['基本信息']['年龄']}")
    print(f"专业: {updated_profile['基本信息']['专业']}")
    print(f"目标岗位: {updated_profile['求职意向']['目标岗位']}")
    print(f"已有技能: {', '.join(updated_profile['技能与兴趣']['已有技能'])}")
    print(f"项目经历: {', '.join(updated_profile['技能与兴趣']['项目经历'])}")
    print(f"兴趣方向: {', '.join(updated_profile['技能与兴趣']['兴趣方向'])}")
    
    # Show changes
    print("\n📈 变化对比:")
    original_skills = set(profile['技能与兴趣']['已有技能'])
    updated_skills = set(updated_profile['技能与兴趣']['已有技能'])
    new_skills = updated_skills - original_skills
    
    if new_skills:
        print(f"新增技能: {', '.join(new_skills)}")
    
    original_position = profile['求职意向']['目标岗位']
    updated_position = updated_profile['求职意向']['目标岗位']
    if original_position != updated_position:
        print(f"目标岗位变更: {original_position} → {updated_position}")
    
    print("\n✨ 档案编辑功能演示完成！")

def demo_conversation_export():
    """Demonstrate conversation export functionality"""
    print("\n📄 Demo: Conversation Export")
    print("=" * 50)
    
    from modules.user_profile import profile_manager
    
    # Add some demo conversations
    conversations = [
        ("我想了解后端开发的学习路径", "后端开发学习路径包括：Java基础 → Spring框架 → 数据库设计 → 微服务架构"),
        ("如何提升我的编程技能？", "建议您：1. 多做项目实践 2. 阅读优秀代码 3. 参与开源项目 4. 学习设计模式"),
        ("后端开发的就业前景如何？", "后端开发就业前景良好，市场需求稳定，薪资水平较高，发展空间广阔")
    ]
    
    print("📝 添加演示对话记录...")
    for user_msg, assistant_msg in conversations:
        profile_manager.add_conversation(user_msg, assistant_msg)
    
    print("✅ 对话记录添加完成")
    
    # Get conversation history
    profile = profile_manager.get_profile()
    history = profile.get("行为数据", {}).get("历史对话", [])
    
    print(f"\n📊 共有 {len(history)} 条对话记录")
    
    # Show export format
    print("\n📄 导出格式预览:")
    print("-" * 30)
    
    for i, conv in enumerate(history[-2:], len(history)-1):  # Show last 2 conversations
        if isinstance(conv, dict):
            timestamp = conv.get("timestamp", "未知时间")
            user_msg = conv.get("user", "")
            assistant_msg = conv.get("assistant", "")
            print(f"## 对话 {i} ({timestamp})")
            print(f"**用户**: {user_msg}")
            print(f"**助手**: {assistant_msg[:100]}...")
            print()
    
    print("✨ 对话导出功能演示完成！")

def demo_gradio_features():
    """Show what features are available in Gradio interface"""
    print("\n🌐 Gradio Web Interface Features")
    print("=" * 50)
    
    features = [
        "👤 个人信息设置表单 - 首次使用时填写基本信息",
        "💬 智能聊天界面 - 与AI助手进行自然语言对话",
        "🔧 个人信息编辑 - 随时更新和修改个人档案",
        "📊 实时统计面板 - 显示用户活动统计和完整度",
        "📈 可视化图表 - 技能雷达图和学习时间线",
        "📄 对话记录导出 - 导出完整的对话历史",
        "🎯 快捷操作按钮 - 一键触发职业规划、技能提升、行业咨询",
        "🔄 动态画像更新 - 从对话中自动提取新信息",
        "📱 响应式设计 - 支持不同屏幕尺寸"
    ]
    
    for feature in features:
        print(f"✅ {feature}")
    
    print("\n🚀 启动方式:")
    print("1. 设置 OpenAI API 密钥: export OPENAI_API_KEY='your-key'")
    print("2. 运行应用: python main.py")
    print("3. 访问: http://localhost:7860")
    
    print("\n📝 使用流程:")
    print("1. 首次访问 → 填写个人信息表单")
    print("2. 完成设置 → 进入聊天界面")
    print("3. 随时编辑 → 点击'编辑个人信息'按钮")
    print("4. 查看统计 → 右侧面板显示个人概览")
    print("5. 导出记录 → 点击'导出对话记录'按钮")

def main():
    """Run all demos"""
    print("🎯 Career Guidance AI - Profile Editing Demo")
    print("=" * 60)
    print("演示个人信息编辑和管理功能")
    print("=" * 60)
    
    try:
        demo_profile_editing()
        demo_conversation_export()
        demo_gradio_features()
        
        print("\n🎉 所有演示完成！")
        print("\n💡 提示:")
        print("- 个人信息可以随时编辑和更新")
        print("- 系统会自动保存所有变更")
        print("- 对话记录支持导出为Markdown格式")
        print("- Web界面提供完整的用户体验")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
