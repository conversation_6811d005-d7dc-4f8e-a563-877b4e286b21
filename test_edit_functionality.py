#!/usr/bin/env python3
"""
Test script for profile editing functionality
"""
import os
import sys

def test_skill_deletion():
    """Test that skills can be deleted from profile"""
    print("🧪 Testing Skill Deletion Functionality")
    print("=" * 50)
    
    from modules.user_profile import profile_manager
    
    # Initialize profile with skills
    print("📝 初始化用户档案...")
    initial_data = {
        "name": "测试用户",
        "age": 25,
        "education": "本科",
        "major": "计算机科学",
        "location": "北京",
        "target_industry": "互联网/IT",
        "target_position": "全栈开发工程师",
        "skills": ["Java", "Python", "JavaScript", "React", "Spring", "MySQL"],
        "projects": ["电商网站", "管理系统", "移动应用"],
        "interests": ["Web开发", "移动开发", "数据库设计"],
        "job_status": "在职求职"
    }
    
    success = profile_manager.initialize_profile(initial_data)
    if not success:
        print("❌ 初始化失败")
        return False
    
    profile = profile_manager.get_profile()
    original_skills = profile['技能与兴趣']['已有技能']
    original_projects = profile['技能与兴趣']['项目经历']
    
    print(f"✅ 原始技能: {', '.join(original_skills)}")
    print(f"✅ 原始项目: {', '.join(original_projects)}")
    
    # Test deletion by updating with fewer items
    print("\n🔄 测试删除技能和项目...")
    updated_data = {
        "基本信息": {
            "姓名": "测试用户",
            "年龄": 25,
            "学历": "本科",
            "专业": "计算机科学",
            "毕业时间": "",
            "所在地": "北京"
        },
        "求职意向": {
            "目标行业": "互联网/IT",
            "目标岗位": "前端开发工程师",  # 改变岗位
            "期望薪资": "",
            "求职状态": "在职求职"
        },
        "技能与兴趣": {
            "已有技能": ["JavaScript", "React", "Vue"],  # 删除了Java, Python, Spring, MySQL
            "项目经历": ["电商网站"],  # 删除了管理系统和移动应用
            "兴趣方向": ["Web开发"]  # 删除了移动开发和数据库设计
        }
    }
    
    # Use replace_lists=True to allow deletion
    success = profile_manager.update_profile(updated_data, replace_lists=True)
    if not success:
        print("❌ 更新失败")
        return False
    
    updated_profile = profile_manager.get_profile()
    new_skills = updated_profile['技能与兴趣']['已有技能']
    new_projects = updated_profile['技能与兴趣']['项目经历']
    new_interests = updated_profile['技能与兴趣']['兴趣方向']
    
    print(f"✅ 更新后技能: {', '.join(new_skills)}")
    print(f"✅ 更新后项目: {', '.join(new_projects)}")
    print(f"✅ 更新后兴趣: {', '.join(new_interests)}")
    
    # Verify deletion worked
    deleted_skills = set(original_skills) - set(new_skills)
    deleted_projects = set(original_projects) - set(new_projects)
    
    print(f"\n📉 删除的技能: {', '.join(deleted_skills)}")
    print(f"📉 删除的项目: {', '.join(deleted_projects)}")
    
    # Check if deletion actually worked
    if len(new_skills) < len(original_skills) and len(new_projects) < len(original_projects):
        print("✅ 技能和项目删除功能正常工作！")
        return True
    else:
        print("❌ 删除功能未正常工作")
        return False

def test_radar_chart_update():
    """Test that radar chart updates with new skills"""
    print("\n📊 Testing Radar Chart Update")
    print("=" * 50)
    
    try:
        from modules.visualization import viz_manager
        from modules.user_profile import profile_manager
        
        # Get current profile
        profile = profile_manager.get_profile()
        skills = profile['技能与兴趣']['已有技能']
        position = profile['求职意向']['目标岗位']
        
        print(f"📋 当前技能: {', '.join(skills)}")
        print(f"📋 目标岗位: {position}")
        
        # Generate radar chart
        print("🎯 生成技能雷达图...")
        chart_path = viz_manager.create_skill_radar_chart(skills, position)
        
        if chart_path and os.path.exists(chart_path):
            print(f"✅ 雷达图生成成功: {chart_path}")
            
            # Check file modification time to ensure it's recent
            import time
            file_time = os.path.getmtime(chart_path)
            current_time = time.time()
            
            if current_time - file_time < 60:  # Within last minute
                print("✅ 雷达图是最新生成的")
                return True
            else:
                print("⚠️  雷达图可能不是最新的")
                return False
        else:
            print("❌ 雷达图生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 雷达图测试出错: {e}")
        return False

def test_profile_completeness():
    """Test profile completeness calculation"""
    print("\n📈 Testing Profile Completeness")
    print("=" * 50)
    
    from agents.chat_agent import chat_agent
    
    # Get user stats
    stats = chat_agent.get_user_stats()
    
    print(f"📊 用户统计: {stats}")
    
    if 'profile_completeness' in stats:
        completeness = stats['profile_completeness']
        print(f"✅ 档案完整度: {completeness:.1f}%")
        
        if completeness > 0:
            print("✅ 档案完整度计算正常")
            return True
        else:
            print("❌ 档案完整度计算异常")
            return False
    else:
        print("❌ 无法获取档案完整度")
        return False

def test_conversation_export():
    """Test conversation export functionality"""
    print("\n📄 Testing Conversation Export")
    print("=" * 50)
    
    from modules.user_profile import profile_manager
    
    # Add a test conversation
    profile_manager.add_conversation(
        "测试编辑功能后的对话",
        "编辑功能测试完成，技能删除和雷达图更新都正常工作"
    )
    
    # Get conversation history
    profile = profile_manager.get_profile()
    history = profile.get("行为数据", {}).get("历史对话", [])
    
    print(f"📊 对话记录数量: {len(history)}")
    
    if history:
        latest = history[-1]
        print(f"✅ 最新对话: {latest.get('user', '')[:50]}...")
        print("✅ 对话导出功能正常")
        return True
    else:
        print("❌ 无对话记录")
        return False

def main():
    """Run all edit functionality tests"""
    print("🧪 Profile Editing Functionality Tests")
    print("=" * 60)
    print("测试个人信息编辑的核心功能")
    print("=" * 60)
    
    tests = [
        ("技能删除功能", test_skill_deletion),
        ("雷达图更新", test_radar_chart_update),
        ("档案完整度", test_profile_completeness),
        ("对话导出", test_conversation_export)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有编辑功能测试通过！")
        print("\n✨ 功能确认:")
        print("- ✅ 技能和项目可以删除")
        print("- ✅ 雷达图实时更新")
        print("- ✅ 档案完整度正确计算")
        print("- ✅ 对话记录正常导出")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
