"""
Helper utility functions for the Career Guidance AI Assistant
"""
import json
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any
from config import INTENT_KEYWORDS

def format_user_profile_for_prompt(user_profile: Dict) -> str:
    """
    Format user profile dictionary into a readable string for LLM prompts
    
    Args:
        user_profile: User profile dictionary
        
    Returns:
        Formatted string representation
    """
    basic_info = user_profile.get("基本信息", {})
    job_intent = user_profile.get("求职意向", {})
    skills = user_profile.get("技能与兴趣", {})
    
    formatted = f"""姓名：{basic_info.get('姓名', '未填写')}
年龄：{basic_info.get('年龄', '未填写')}
学历：{basic_info.get('学历', '未填写')}
专业：{basic_info.get('专业', '未填写')}
所在地：{basic_info.get('所在地', '未填写')}
目标行业：{job_intent.get('目标行业', '未填写')}
目标岗位：{job_intent.get('目标岗位', '未填写')}
求职状态：{job_intent.get('求职状态', '未填写')}
已有技能：{', '.join(skills.get('已有技能', []))}
项目经历：{', '.join(skills.get('项目经历', []))}
兴趣方向：{', '.join(skills.get('兴趣方向', []))}"""
    
    return formatted

def classify_intent(text: str) -> str:
    """
    Classify user intent based on keywords
    
    Args:
        text: User input text
        
    Returns:
        Intent category: 'career', 'skill', 'industry', or 'general'
    """
    text_lower = text.lower()
    
    for intent, keywords in INTENT_KEYWORDS.items():
        if any(keyword in text_lower for keyword in keywords):
            return intent
    
    return "general"

def split_text_and_json(text: str) -> Tuple[str, str]:
    """
    Split text containing both narrative and JSON parts
    
    Args:
        text: Input text that may contain JSON
        
    Returns:
        Tuple of (text_part, json_part)
    """
    # Find JSON-like structure
    json_start = text.find('{')
    json_end = text.rfind('}') + 1
    
    if json_start != -1 and json_end > json_start:
        text_part = text[:json_start].strip()
        json_part = text[json_start:json_end]
        return text_part, json_part
    else:
        return text, ""

def merge_profile(old_profile: Dict, new_info: Dict) -> Dict:
    """
    Merge new information into existing user profile
    
    Args:
        old_profile: Existing user profile
        new_info: New information to merge
        
    Returns:
        Updated profile dictionary
    """
    def merge_recursive(old_dict: Dict, new_dict: Dict) -> Dict:
        for key, value in new_dict.items():
            if key not in old_dict:
                old_dict[key] = value
            else:
                if isinstance(value, dict) and isinstance(old_dict[key], dict):
                    old_dict[key] = merge_recursive(old_dict[key], value)
                elif isinstance(value, list) and isinstance(old_dict[key], list):
                    # Merge lists and remove duplicates
                    old_dict[key] = list(set(old_dict[key] + value))
                else:
                    # Direct replacement
                    old_dict[key] = value
        return old_dict
    
    return merge_recursive(old_profile.copy(), new_info)

def parse_skill_and_duration(text: str) -> Tuple[str, str]:
    """
    Extract target skill and learning duration from user input
    
    Args:
        text: User input text
        
    Returns:
        Tuple of (target_skill, duration)
    """
    # Simple extraction - can be enhanced with NLP
    duration_patterns = [
        r'(\d+)\s*个?月',
        r'(\d+)\s*周',
        r'(\d+)\s*天'
    ]
    
    duration = "三个月"  # default
    for pattern in duration_patterns:
        match = re.search(pattern, text)
        if match:
            num = match.group(1)
            if '月' in match.group(0):
                duration = f"{num}个月"
            elif '周' in match.group(0):
                duration = f"{num}周"
            elif '天' in match.group(0):
                duration = f"{num}天"
            break
    
    # Extract skill - simple keyword matching
    skill_keywords = ['python', 'java', 'javascript', 'react', 'vue', 'node', 'html', 'css', 
                     'sql', '数据分析', '机器学习', '前端', '后端', '全栈']
    
    target_skill = "编程技能"  # default
    text_lower = text.lower()
    for skill in skill_keywords:
        if skill in text_lower:
            target_skill = skill
            break
    
    return target_skill, duration

def now_str() -> str:
    """Get current timestamp as string"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def format_json_as_markdown_table(data: Dict, title: str = "") -> str:
    """
    Format JSON data as a markdown table
    
    Args:
        data: Dictionary to format
        title: Optional title for the table
        
    Returns:
        Markdown formatted string
    """
    if not data:
        return ""
    
    markdown = f"\n### {title}\n\n" if title else "\n"
    
    if isinstance(data, dict):
        markdown += "| 项目 | 内容 |\n"
        markdown += "|------|------|\n"
        for key, value in data.items():
            if isinstance(value, list):
                value_str = ", ".join(str(v) for v in value)
            else:
                value_str = str(value)
            markdown += f"| {key} | {value_str} |\n"
    
    return markdown
