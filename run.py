#!/usr/bin/env python3
"""
Simple startup script for Career Guidance AI Assistant
"""
import os
import sys
import subprocess

def check_python_version():
    """Check if Python version is compatible"""
    version_str = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"✅ Python version: {version_str}")

    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {version_str}")
        return False

    return True

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'gradio',
        'openai', 
        'matplotlib',
        'pandas',
        'numpy',
        'plotly',
        'python-dotenv',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (missing)")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages)
            print("✅ All packages installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install packages")
            print("Please run: pip install -r requirements.txt")
            return False
    
    return True

def check_env_file():
    """Check if .env file exists and has API key"""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("⚠️  .env file not found, creating from template...")
            with open('.env.example', 'r') as src, open('.env', 'w') as dst:
                dst.write(src.read())
            print("✅ .env file created from template")
            print("📝 Please edit .env file and add your OpenAI API key")
            return False
        else:
            print("❌ No .env file or template found")
            return False
    
    # Check if API key is set
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key or api_key == 'your-openai-api-key-here':
        print("⚠️  OpenAI API key not configured in .env file")
        print("📝 Please edit .env file and add your OpenAI API key")
        return False
    
    print("✅ OpenAI API key configured")
    return True

def run_tests():
    """Run system tests"""
    print("\n🧪 Running system tests...")
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Some tests failed")
            print(result.stdout)
            return False
    except FileNotFoundError:
        print("⚠️  test_system.py not found, skipping tests")
        return True

def main():
    """Main startup function"""
    print("🚀 Career Guidance AI Assistant - Startup Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    print("\n📦 Checking dependencies...")
    if not check_dependencies():
        return False
    
    print("\n🔧 Checking configuration...")
    env_ok = check_env_file()
    
    print("\n🧪 Running tests...")
    tests_ok = run_tests()
    
    if env_ok and tests_ok:
        print("\n🎉 System ready! Starting application...")
        print("=" * 50)
        
        # Import and run the main application
        try:
            from main import main as app_main
            app_main()
        except KeyboardInterrupt:
            print("\n👋 Application stopped by user")
        except Exception as e:
            print(f"\n❌ Application error: {e}")
            return False
    else:
        print("\n⚠️  Please fix the issues above before starting the application")
        if not env_ok:
            print("1. Configure your OpenAI API key in the .env file")
        if not tests_ok:
            print("2. Fix any test failures")
        print("\nThen run: python main.py")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
