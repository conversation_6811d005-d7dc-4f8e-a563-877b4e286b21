"""
Central Chat Agent for Career Guidance AI Assistant
"""
import logging
from typing import Dict, <PERSON><PERSON>, Optional
from utils.llm_client import llm_client
from utils.helpers import classify_intent, format_json_as_markdown_table
from modules.user_profile import profile_manager
from modules.visualization import viz_manager
from agents.career_planning_agent import career_agent
from agents.skill_enhancement_agent import skill_agent
from agents.industry_expert_agent import industry_agent

logger = logging.getLogger(__name__)

class ChatAgent:
    """Central dispatcher for routing user requests to specialized agents"""
    
    def __init__(self):
        self.conversation_history = []
    
    def process_message(self, user_message: str) -> Tuple[str, Optional[str], Dict]:
        """
        Process user message and route to appropriate agent
        
        Args:
            user_message: User's input message
            
        Returns:
            Tuple of (response_text, chart_path, metadata)
        """
        try:
            # Get current user profile
            user_profile = profile_manager.get_profile()
            
            # Update user profile with any new information from the message
            self._update_profile_from_message(user_message, user_profile)
            
            # Classify user intent
            intent = classify_intent(user_message)
            logger.info(f"Classified intent: {intent}")
            
            # Route to appropriate agent
            response_data = self._route_to_agent(intent, user_message, user_profile)
            
            # Generate visualization if applicable
            chart_path = self._generate_visualization(intent, response_data, user_profile)
            
            # Format response
            response_text = self._format_response(response_data, intent)
            
            # Save conversation to history
            profile_manager.add_conversation(user_message, response_text)
            
            # Prepare metadata
            metadata = {
                "intent": intent,
                "has_structured_data": bool(response_data.get("plan") or response_data.get("summary")),
                "chart_generated": chart_path is not None
            }
            
            return response_text, chart_path, metadata
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return f"处理消息时出现错误：{str(e)}", None, {"intent": "error"}
    
    def _update_profile_from_message(self, user_message: str, user_profile: Dict) -> None:
        """Update user profile with new information from message"""
        try:
            if llm_client:
                new_info = llm_client.extract_user_profile_info(user_message, user_profile)
                if new_info:
                    profile_manager.update_profile(new_info)
                    logger.info(f"Updated profile with new info: {new_info}")
        except Exception as e:
            logger.warning(f"Failed to update profile from message: {e}")
    
    def _route_to_agent(self, intent: str, user_message: str, user_profile: Dict) -> Dict:
        """Route message to appropriate specialized agent"""
        try:
            if intent == "career":
                return career_agent.process_request(user_message, user_profile)
            elif intent == "skill":
                return skill_agent.process_request(user_message, user_profile)
            elif intent == "industry":
                return industry_agent.process_request(user_message, user_profile)
            else:
                return self._handle_general_chat(user_message, user_profile)
        except Exception as e:
            logger.error(f"Error routing to agent: {e}")
            return {"text": f"处理请求时出现错误：{str(e)}"}
    
    def _handle_general_chat(self, user_message: str, user_profile: Dict) -> Dict:
        """Handle general chat that doesn't fit specific categories"""
        try:
            if not llm_client:
                return {"text": "抱歉，AI服务暂时不可用，请检查API配置。"}
            
            system_prompt = """你是一个求职助手的通用对话模块，用户可能会问一些与求职相关的杂项问题。
请在求职和职业发展的范围内给出专业、礼貌、详细的解答。
如果问题超出求职范围，请礼貌地引导用户回到求职相关话题。"""
            
            user_prompt = f"用户问题：{user_message}"
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = llm_client.chat_completion(messages)
            return {"text": response}
            
        except Exception as e:
            logger.error(f"Error in general chat: {e}")
            return {"text": f"处理通用对话时出现错误：{str(e)}"}
    
    def _generate_visualization(self, intent: str, response_data: Dict, user_profile: Dict) -> Optional[str]:
        """Generate appropriate visualization based on intent and response"""
        try:
            if intent == "career" and response_data.get("plan"):
                # Generate skill radar chart for career planning
                user_skills = profile_manager.get_skills_list()
                target_position = profile_manager.get_target_position()
                if user_skills and target_position:
                    return viz_manager.create_skill_radar_chart(user_skills, target_position)
            
            elif intent == "skill" and response_data.get("plan"):
                # Generate learning timeline for skill enhancement
                return viz_manager.create_learning_timeline(response_data["plan"])
            
            elif intent == "industry" and response_data.get("summary"):
                # Generate industry overview chart
                return viz_manager.create_industry_overview_chart(response_data["summary"])
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to generate visualization: {e}")
            return None
    
    def _format_response(self, response_data: Dict, intent: str) -> str:
        """Format response text with structured data"""
        try:
            response_text = response_data.get("text", "")
            
            # Add structured data formatting
            if intent == "career" and response_data.get("plan"):
                plan_table = format_json_as_markdown_table(response_data["plan"], "职业规划方案")
                response_text += plan_table
            
            elif intent == "skill" and response_data.get("plan"):
                plan_table = format_json_as_markdown_table(response_data["plan"], "学习计划")
                response_text += plan_table
            
            elif intent == "industry" and response_data.get("summary"):
                summary_table = format_json_as_markdown_table(response_data["summary"], "行业概览")
                response_text += summary_table
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error formatting response: {e}")
            return response_data.get("text", "格式化响应时出现错误")
    
    def get_conversation_history(self) -> list:
        """Get conversation history from user profile"""
        try:
            profile = profile_manager.get_profile()
            return profile.get("行为数据", {}).get("历史对话", [])
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def clear_conversation_history(self) -> bool:
        """Clear conversation history"""
        try:
            profile = profile_manager.get_profile()
            profile.setdefault("行为数据", {})["历史对话"] = []
            return profile_manager.update_profile(profile)
        except Exception as e:
            logger.error(f"Error clearing conversation history: {e}")
            return False
    
    def get_user_stats(self) -> Dict:
        """Get user statistics and insights"""
        try:
            profile = profile_manager.get_profile()
            history = profile.get("行为数据", {}).get("历史对话", [])
            
            stats = {
                "total_conversations": len(history),
                "profile_completeness": self._calculate_profile_completeness(profile),
                "last_activity": profile.get("行为数据", {}).get("最近更新时间", "未知"),
                "skills_count": len(profile.get("技能与兴趣", {}).get("已有技能", [])),
                "projects_count": len(profile.get("技能与兴趣", {}).get("项目经历", []))
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return {"error": str(e)}
    
    def _calculate_profile_completeness(self, profile: Dict) -> float:
        """Calculate profile completeness percentage"""
        try:
            required_fields = [
                profile.get("基本信息", {}).get("学历"),
                profile.get("基本信息", {}).get("专业"),
                profile.get("求职意向", {}).get("目标行业"),
                profile.get("求职意向", {}).get("目标岗位"),
                profile.get("技能与兴趣", {}).get("已有技能")
            ]
            
            filled_fields = sum(1 for field in required_fields if field)
            return (filled_fields / len(required_fields)) * 100
            
        except Exception as e:
            logger.error(f"Error calculating profile completeness: {e}")
            return 0.0

# Global chat agent instance
chat_agent = ChatAgent()
