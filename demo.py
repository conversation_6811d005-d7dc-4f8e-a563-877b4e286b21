#!/usr/bin/env python3
"""
Demo script for Career Guidance AI Assistant
Shows how the system works without requiring OpenAI API
"""
import os
import sys

def demo_user_profile():
    """Demonstrate user profile management"""
    print("🔧 Demo: User Profile Management")
    print("=" * 40)
    
    from modules.user_profile import profile_manager
    
    # Initialize a demo profile
    demo_data = {
        "name": "张小明",
        "age": 22,
        "education": "本科",
        "major": "计算机科学与技术",
        "location": "北京",
        "target_industry": "互联网/IT",
        "target_position": "前端开发工程师",
        "skills": ["HTML", "CSS", "JavaScript", "Python"],
        "projects": ["个人博客网站", "在线商城前端"],
        "interests": ["Web开发", "用户体验设计"],
        "job_status": "大三在校学生"
    }
    
    print("📝 初始化用户档案...")
    success = profile_manager.initialize_profile(demo_data)
    if success:
        print("✅ 用户档案创建成功")
    else:
        print("❌ 用户档案创建失败")
        return
    
    # Show profile
    profile = profile_manager.get_profile()
    print("\n👤 用户档案信息:")
    print(f"姓名: {profile['基本信息']['姓名']}")
    print(f"专业: {profile['基本信息']['专业']}")
    print(f"目标岗位: {profile['求职意向']['目标岗位']}")
    print(f"已有技能: {', '.join(profile['技能与兴趣']['已有技能'])}")
    
    # Add conversation
    profile_manager.add_conversation(
        "我想了解前端开发的学习路径",
        "前端开发学习路径包括：HTML/CSS基础 → JavaScript → 前端框架(React/Vue) → 工程化工具"
    )
    print("✅ 对话记录已添加")

def demo_intent_classification():
    """Demonstrate intent classification"""
    print("\n🎯 Demo: Intent Classification")
    print("=" * 40)
    
    from utils.helpers import classify_intent
    
    test_cases = [
        "我想做职业规划，请给我一些建议",
        "如何学习React框架？给我一个学习计划",
        "前端开发行业的薪资水平如何？",
        "你好，我是新用户",
        "我想转行做数据分析师",
        "Python编程需要掌握哪些技能？",
        "互联网行业的发展前景怎么样？"
    ]
    
    for question in test_cases:
        intent = classify_intent(question)
        intent_name = {
            "career": "职业规划",
            "skill": "技能提升", 
            "industry": "行业咨询",
            "general": "通用对话"
        }.get(intent, "未知")
        
        print(f"问题: {question}")
        print(f"分类: {intent_name} ({intent})")
        print("-" * 40)

def demo_visualization():
    """Demonstrate visualization generation"""
    print("\n📊 Demo: Visualization Generation")
    print("=" * 40)
    
    from modules.visualization import viz_manager
    
    # Demo skill radar chart
    print("🎯 生成技能雷达图...")
    user_skills = ["HTML", "CSS", "JavaScript", "Python"]
    target_position = "前端开发工程师"
    
    chart_path = viz_manager.create_skill_radar_chart(user_skills, target_position)
    if chart_path and os.path.exists(chart_path):
        print(f"✅ 技能雷达图已生成: {chart_path}")
    else:
        print("⚠️  技能雷达图生成失败")
    
    # Demo learning timeline
    print("\n📅 生成学习时间线...")
    learning_plan = {
        "第1-2周": ["复习HTML/CSS基础", "学习CSS Grid和Flexbox"],
        "第3-4周": ["深入学习JavaScript", "DOM操作和事件处理"],
        "第5-6周": ["学习ES6+新特性", "异步编程和Promise"],
        "第7-8周": ["React基础入门", "组件和状态管理"],
        "第9-10周": ["React进阶", "路由和状态管理库"],
        "第11-12周": ["项目实战", "完整前端项目开发"]
    }
    
    timeline_path = viz_manager.create_learning_timeline(learning_plan)
    if timeline_path and os.path.exists(timeline_path):
        print(f"✅ 学习时间线已生成: {timeline_path}")
    else:
        print("⚠️  学习时间线生成失败")

def demo_helper_functions():
    """Demonstrate helper functions"""
    print("\n🛠️  Demo: Helper Functions")
    print("=" * 40)
    
    from utils.helpers import parse_skill_and_duration, format_user_profile_for_prompt
    from modules.user_profile import profile_manager
    
    # Test skill parsing
    test_inputs = [
        "我想学习Python，给我一个三个月的计划",
        "如何在两周内掌握React基础？",
        "学习数据分析需要多长时间？",
        "给我一个Java学习路线图"
    ]
    
    print("🔍 技能和时长解析:")
    for text in test_inputs:
        skill, duration = parse_skill_and_duration(text)
        print(f"输入: {text}")
        print(f"技能: {skill}, 时长: {duration}")
        print("-" * 30)
    
    # Test profile formatting
    print("\n📋 用户档案格式化:")
    profile = profile_manager.get_profile()
    formatted = format_user_profile_for_prompt(profile)
    print(formatted[:200] + "..." if len(formatted) > 200 else formatted)

def demo_mock_chat():
    """Demonstrate mock chat without API calls"""
    print("\n💬 Demo: Mock Chat Interaction")
    print("=" * 40)
    
    # Mock responses for different intents
    mock_responses = {
        "career": """基于您的背景，我为您制定以下职业发展规划：

**短期目标 (6个月内):**
- 完善前端基础技能，熟练掌握HTML5、CSS3、JavaScript ES6+
- 学习一个主流前端框架 (推荐React或Vue)
- 完成2-3个个人项目，建立作品集

**中期目标 (1-2年):**
- 掌握前端工程化工具 (Webpack、Vite等)
- 学习TypeScript，提升代码质量
- 参与开源项目或实习，积累实战经验

**长期目标 (3-5年):**
- 成为高级前端工程师，具备独立开发能力
- 学习全栈技能，了解后端和数据库
- 培养团队协作和项目管理能力""",
        
        "skill": """为您制定React学习计划：

**第1-2周: React基础**
- 学习JSX语法和组件概念
- 掌握props和state的使用
- 练习创建简单的React组件

**第3-4周: React进阶**
- 学习生命周期方法和Hooks
- 掌握事件处理和表单处理
- 练习组件间通信

**第5-6周: 状态管理**
- 学习Context API
- 了解Redux基础概念
- 练习状态管理最佳实践

**推荐资源:**
- React官方文档
- 慕课网React教程
- GitHub上的React项目""",
        
        "industry": """前端开发行业分析：

**行业现状:**
- 市场需求旺盛，岗位数量持续增长
- 技术更新快，需要持续学习
- 薪资水平相对较高

**主要岗位:**
- 前端开发工程师
- 全栈开发工程师  
- 前端架构师
- UI/UX工程师

**薪资参考 (北京地区):**
- 初级: 8-15K
- 中级: 15-25K
- 高级: 25-40K+

**发展趋势:**
- 向全栈方向发展
- 重视用户体验和性能优化
- 微前端和低代码平台兴起"""
    }
    
    questions = [
        ("我想做前端开发的职业规划", "career"),
        ("如何学习React框架？", "skill"),
        ("前端开发的就业前景如何？", "industry")
    ]
    
    for question, intent in questions:
        print(f"\n👤 用户: {question}")
        print(f"🤖 助手: {mock_responses[intent]}")
        print("-" * 60)

def main():
    """Run all demos"""
    print("🚀 Career Guidance AI Assistant - Demo")
    print("=" * 60)
    print("这是一个演示脚本，展示系统的核心功能")
    print("注意：此演示不需要OpenAI API密钥")
    print("=" * 60)
    
    try:
        demo_user_profile()
        demo_intent_classification()
        demo_visualization()
        demo_helper_functions()
        demo_mock_chat()
        
        print("\n🎉 演示完成！")
        print("\n📝 要启动完整的Web应用，请:")
        print("1. 设置OpenAI API密钥")
        print("2. 运行: python main.py")
        print("\n📊 生成的图表文件位于 charts/ 目录")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
