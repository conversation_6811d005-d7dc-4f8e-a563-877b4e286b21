#!/usr/bin/env python3
"""
Enhanced Profile Editing Demo - Shows deletion and real-time radar chart updates
"""
import os
import sys

def demo_skill_deletion():
    """Demonstrate skill and project deletion"""
    print("🗑️  Demo: Skill and Project Deletion")
    print("=" * 50)
    
    from modules.user_profile import profile_manager
    
    # Start with a comprehensive profile
    print("📝 创建完整的用户档案...")
    comprehensive_data = {
        "name": "王开发",
        "age": 26,
        "education": "硕士",
        "major": "软件工程",
        "location": "深圳",
        "target_industry": "互联网/IT",
        "target_position": "全栈开发工程师",
        "skills": [
            "Java", "Spring Boot", "MySQL", "Redis",
            "JavaScript", "React", "Vue", "Node.js",
            "Python", "Django", "Docker", "Kubernetes"
        ],
        "projects": [
            "微服务电商平台",
            "React管理后台",
            "Vue移动端应用",
            "Python数据分析工具",
            "Docker容器化部署",
            "Kubernetes集群管理"
        ],
        "interests": [
            "后端开发", "前端开发", "DevOps",
            "微服务架构", "容器化", "云原生"
        ],
        "job_status": "在职求职"
    }
    
    profile_manager.initialize_profile(comprehensive_data)
    profile = profile_manager.get_profile()
    
    print("👤 原始档案:")
    print(f"   技能数量: {len(profile['技能与兴趣']['已有技能'])}")
    print(f"   项目数量: {len(profile['技能与兴趣']['项目经历'])}")
    print(f"   兴趣数量: {len(profile['技能与兴趣']['兴趣方向'])}")
    print(f"   技能列表: {', '.join(profile['技能与兴趣']['已有技能'][:5])}...")
    
    # Simulate user deciding to focus on frontend only
    print("\n🎯 用户决定专注前端开发，删除后端相关技能...")
    focused_update = {
        "基本信息": {
            "姓名": "王开发",
            "年龄": 26,
            "学历": "硕士",
            "专业": "软件工程",
            "毕业时间": "2023年6月",
            "所在地": "深圳"
        },
        "求职意向": {
            "目标行业": "互联网/IT",
            "目标岗位": "前端开发工程师",  # 改变目标
            "期望薪资": "20-30K",
            "求职状态": "在职求职"
        },
        "技能与兴趣": {
            "已有技能": ["JavaScript", "React", "Vue", "HTML", "CSS"],  # 只保留前端技能
            "项目经历": ["React管理后台", "Vue移动端应用"],  # 只保留前端项目
            "兴趣方向": ["前端开发", "用户体验", "移动开发"]  # 调整兴趣方向
        }
    }
    
    # Use replace_lists=True to enable deletion
    success = profile_manager.update_profile(focused_update, replace_lists=True)
    
    if success:
        updated_profile = profile_manager.get_profile()
        print("✅ 档案更新成功!")
        print("👤 更新后档案:")
        print(f"   技能数量: {len(updated_profile['技能与兴趣']['已有技能'])}")
        print(f"   项目数量: {len(updated_profile['技能与兴趣']['项目经历'])}")
        print(f"   兴趣数量: {len(updated_profile['技能与兴趣']['兴趣方向'])}")
        print(f"   技能列表: {', '.join(updated_profile['技能与兴趣']['已有技能'])}")
        
        # Show what was removed
        original_skills = set(profile['技能与兴趣']['已有技能'])
        new_skills = set(updated_profile['技能与兴趣']['已有技能'])
        removed_skills = original_skills - new_skills
        
        print(f"\n📉 删除的技能: {', '.join(removed_skills)}")
        print("✨ 用户成功精简了技能列表，专注前端发展方向！")
    else:
        print("❌ 更新失败")

def demo_realtime_radar_chart():
    """Demonstrate real-time radar chart updates"""
    print("\n📊 Demo: Real-time Radar Chart Updates")
    print("=" * 50)
    
    from modules.visualization import viz_manager
    from modules.user_profile import profile_manager
    
    # Get current profile
    profile = profile_manager.get_profile()
    current_skills = profile['技能与兴趣']['已有技能']
    current_position = profile['求职意向']['目标岗位']
    
    print(f"📋 当前技能: {', '.join(current_skills)}")
    print(f"📋 目标岗位: {current_position}")
    
    # Generate initial radar chart
    print("\n🎯 生成初始雷达图...")
    chart1_path = viz_manager.create_skill_radar_chart(current_skills, current_position)
    if chart1_path:
        print(f"✅ 初始雷达图: {chart1_path}")
    
    # Simulate skill improvement
    print("\n📈 模拟技能提升：用户学习了新技能...")
    enhanced_skills = current_skills + ["TypeScript", "Next.js", "Tailwind CSS"]
    
    print(f"📋 增强后技能: {', '.join(enhanced_skills)}")
    
    # Generate updated radar chart
    print("🎯 生成更新后雷达图...")
    chart2_path = viz_manager.create_skill_radar_chart(enhanced_skills, current_position)
    if chart2_path:
        print(f"✅ 更新后雷达图: {chart2_path}")
        
        # Check if files are different (by modification time)
        import time
        if chart1_path and chart2_path:
            time1 = os.path.getmtime(chart1_path)
            time2 = os.path.getmtime(chart2_path)
            if time2 > time1:
                print("✅ 雷达图已实时更新！")
            else:
                print("⚠️  雷达图可能未更新")
    
    # Simulate position change
    print("\n🔄 模拟岗位变更：从前端转向全栈...")
    new_position = "全栈开发工程师"
    
    print(f"📋 新目标岗位: {new_position}")
    
    # Generate radar chart for new position
    print("🎯 生成新岗位雷达图...")
    chart3_path = viz_manager.create_skill_radar_chart(enhanced_skills, new_position)
    if chart3_path:
        print(f"✅ 新岗位雷达图: {chart3_path}")
        print("✅ 雷达图根据岗位变更实时调整！")

def demo_gradio_workflow():
    """Show the complete Gradio workflow"""
    print("\n🌐 Demo: Complete Gradio Workflow")
    print("=" * 50)
    
    workflow_steps = [
        "1. 👤 用户首次访问 → 填写个人信息表单",
        "2. 💬 进入聊天界面 → 与AI助手对话",
        "3. 📊 查看右侧面板 → 实时统计和雷达图",
        "4. ✏️  点击'编辑个人信息' → 打开编辑表单",
        "5. 🗑️  删除不需要的技能/项目 → 精简档案",
        "6. 💾 点击'保存修改' → 立即生效",
        "7. 📈 雷达图自动更新 → 反映新的技能组合",
        "8. 📄 导出对话记录 → 保存咨询历史"
    ]
    
    for step in workflow_steps:
        print(f"   {step}")
    
    print("\n🔧 编辑功能特色:")
    features = [
        "✅ 支持删除技能和项目（不只是添加）",
        "✅ 雷达图实时更新（编辑后立即生成新图表）",
        "✅ 表单预填充（显示当前值，方便修改）",
        "✅ 一键取消（可以放弃修改）",
        "✅ 状态反馈（显示保存成功/失败）",
        "✅ 数据持久化（修改立即保存到本地）"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n💡 使用技巧:")
    tips = [
        "🎯 技能字段：用逗号分隔，删除不需要的技能",
        "📁 项目字段：用逗号分隔，可以删除过时的项目",
        "🎨 兴趣字段：用逗号分隔，调整职业兴趣方向",
        "📊 雷达图：会根据目标岗位自动调整评估标准",
        "💾 保存后：统计面板和图表会立即更新",
        "📱 响应式：支持不同屏幕尺寸的设备"
    ]
    
    for tip in tips:
        print(f"   {tip}")

def main():
    """Run enhanced editing demo"""
    print("🚀 Enhanced Profile Editing Demo")
    print("=" * 60)
    print("展示增强的个人信息编辑功能")
    print("包括：技能删除 + 实时雷达图更新")
    print("=" * 60)
    
    try:
        demo_skill_deletion()
        demo_realtime_radar_chart()
        demo_gradio_workflow()
        
        print("\n🎉 增强编辑功能演示完成！")
        print("\n✨ 新功能总结:")
        print("1. ✅ 支持删除技能、项目和兴趣")
        print("2. ✅ 雷达图实时更新反映变化")
        print("3. ✅ 完整的编辑-保存-更新流程")
        print("4. ✅ 用户友好的界面交互")
        
        print("\n🚀 启动完整应用:")
        print("python main.py")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
