#!/usr/bin/env python3
"""
Demo: Dynamic Radar Chart Dimensions Based on Career Direction
演示：根据求职方向动态变化的雷达图维度
"""
import os
import sys

def demo_career_direction_change():
    """演示求职方向变化时雷达图维度的动态变化"""
    print("🎯 Demo: Career Direction Change Impact on Radar Chart")
    print("=" * 60)
    print("演示同一用户改变求职方向时，雷达图维度如何动态调整")
    print("=" * 60)
    
    from modules.visualization import viz_manager
    from modules.user_profile import profile_manager
    
    # 模拟用户：计算机专业学生，有一定编程基础
    user_name = "李同学"
    base_skills = ["Python", "Java", "HTML", "CSS", "JavaScript", "SQL"]
    
    print(f"👤 用户: {user_name}")
    print(f"🛠️  基础技能: {', '.join(base_skills)}")
    
    # 测试不同的求职方向
    career_directions = [
        {
            "position": "前端开发工程师",
            "description": "专注Web前端开发",
            "additional_skills": ["React", "Vue", "Webpack"]
        },
        {
            "position": "后端开发工程师", 
            "description": "专注服务器端开发",
            "additional_skills": ["Spring Boot", "MySQL", "Redis"]
        },
        {
            "position": "数据分析师",
            "description": "专注数据分析和挖掘",
            "additional_skills": ["Pandas", "NumPy", "机器学习"]
        },
        {
            "position": "全栈开发工程师",
            "description": "前后端全栈开发",
            "additional_skills": ["Node.js", "MongoDB", "Docker"]
        },
        {
            "position": "AI工程师",
            "description": "人工智能和机器学习",
            "additional_skills": ["TensorFlow", "PyTorch", "深度学习"]
        }
    ]
    
    print("\n🔄 测试不同求职方向的雷达图维度变化:")
    print("=" * 60)
    
    for i, direction in enumerate(career_directions, 1):
        position = direction["position"]
        description = direction["description"]
        additional_skills = direction["additional_skills"]
        
        # 组合技能（基础技能 + 方向相关技能）
        combined_skills = base_skills + additional_skills
        
        print(f"\n📋 方向 {i}: {position}")
        print(f"📝 描述: {description}")
        print(f"🛠️  技能组合: {', '.join(combined_skills)}")
        
        # 生成该方向的技能要求维度
        if hasattr(viz_manager, '_generate_skill_requirements'):
            requirements = viz_manager._generate_skill_requirements(position, combined_skills)
        else:
            requirements = viz_manager._get_static_skill_requirements(position)
        
        print("🎯 该方向的技能维度:")
        for skill, score in requirements.items():
            print(f"   • {skill}: {score}分")
        
        # 生成雷达图
        chart_path = viz_manager.create_skill_radar_chart(combined_skills, position)
        if chart_path and os.path.exists(chart_path):
            file_size = os.path.getsize(chart_path)
            print(f"✅ 雷达图已生成: {chart_path} ({file_size} bytes)")
        else:
            print("❌ 雷达图生成失败")
        
        print("-" * 50)

def demo_dimension_comparison():
    """对比不同岗位的维度差异"""
    print("\n📊 Demo: Dimension Comparison Across Positions")
    print("=" * 60)
    
    from modules.visualization import viz_manager
    
    positions = [
        "前端开发工程师",
        "后端开发工程师", 
        "数据分析师",
        "产品经理",
        "UI设计师"
    ]
    
    all_dimensions = {}
    
    print("🔍 收集各岗位的技能维度:")
    for position in positions:
        requirements = viz_manager._get_static_skill_requirements(position)
        all_dimensions[position] = set(requirements.keys())
        
        print(f"\n📋 {position}:")
        for skill in requirements.keys():
            print(f"   • {skill}")
    
    print("\n🔍 维度差异分析:")
    print("=" * 40)
    
    # 找出每个岗位的独有维度
    for position in positions:
        current_dims = all_dimensions[position]
        other_dims = set()
        for other_pos in positions:
            if other_pos != position:
                other_dims.update(all_dimensions[other_pos])
        
        unique_dims = current_dims - other_dims
        if unique_dims:
            print(f"\n🎯 {position} 独有维度:")
            for dim in unique_dims:
                print(f"   • {dim}")
        else:
            print(f"\n⚠️  {position} 无独有维度")
    
    # 找出共同维度
    common_dims = all_dimensions[positions[0]]
    for position in positions[1:]:
        common_dims = common_dims.intersection(all_dimensions[position])
    
    if common_dims:
        print(f"\n🤝 所有岗位共同维度:")
        for dim in common_dims:
            print(f"   • {dim}")
    else:
        print(f"\n✅ 各岗位维度完全不同，体现了专业性")

def demo_llm_vs_static():
    """对比LLM动态生成与静态配置的差异"""
    print("\n🤖 Demo: LLM Dynamic vs Static Requirements")
    print("=" * 60)
    
    from modules.visualization import viz_manager
    
    test_position = "区块链开发工程师"
    test_skills = ["Solidity", "Web3", "智能合约", "以太坊"]
    
    print(f"📋 测试岗位: {test_position}")
    print(f"🛠️  用户技能: {', '.join(test_skills)}")
    
    # 获取静态要求
    print("\n📊 静态配置的技能要求:")
    static_requirements = viz_manager._get_static_skill_requirements(test_position)
    for skill, score in static_requirements.items():
        print(f"   • {skill}: {score}分")
    
    # 获取动态生成的要求（如果有LLM）
    print("\n🤖 LLM动态生成的技能要求:")
    try:
        dynamic_requirements = viz_manager._generate_skill_requirements(test_position, test_skills)
        for skill, score in dynamic_requirements.items():
            print(f"   • {skill}: {score}分")
        
        # 对比差异
        static_dims = set(static_requirements.keys())
        dynamic_dims = set(dynamic_requirements.keys())
        
        print("\n🔍 差异分析:")
        print(f"静态维度数量: {len(static_dims)}")
        print(f"动态维度数量: {len(dynamic_dims)}")
        
        only_static = static_dims - dynamic_dims
        only_dynamic = dynamic_dims - static_dims
        
        if only_static:
            print(f"仅静态配置有: {', '.join(only_static)}")
        if only_dynamic:
            print(f"仅动态生成有: {', '.join(only_dynamic)}")
        
        if dynamic_dims != static_dims:
            print("✅ LLM能够生成更精准的技能维度")
        else:
            print("⚠️  动态生成与静态配置相同")
            
    except Exception as e:
        print(f"❌ LLM动态生成失败: {e}")
        print("💡 将使用静态配置作为后备方案")

def demo_user_skill_influence():
    """演示用户技能对维度生成的影响"""
    print("\n👤 Demo: User Skills Influence on Dimensions")
    print("=" * 60)
    
    from modules.visualization import viz_manager
    
    position = "全栈开发工程师"
    
    skill_scenarios = [
        {
            "name": "前端偏向",
            "skills": ["React", "Vue", "JavaScript", "HTML", "CSS"]
        },
        {
            "name": "后端偏向", 
            "skills": ["Java", "Spring", "MySQL", "Redis", "Docker"]
        },
        {
            "name": "均衡发展",
            "skills": ["JavaScript", "React", "Node.js", "MongoDB", "Python"]
        },
        {
            "name": "无相关技能",
            "skills": ["Photoshop", "Excel", "PowerPoint"]
        }
    ]
    
    print(f"📋 目标岗位: {position}")
    
    for scenario in skill_scenarios:
        name = scenario["name"]
        skills = scenario["skills"]
        
        print(f"\n🎯 场景: {name}")
        print(f"🛠️  技能: {', '.join(skills)}")
        
        # 生成要求（LLM会考虑用户技能）
        try:
            requirements = viz_manager._generate_skill_requirements(position, skills)
            print("📊 生成的技能维度:")
            for skill, score in requirements.items():
                print(f"   • {skill}: {score}分")
        except Exception as e:
            print(f"❌ 生成失败: {e}")

def main():
    """运行所有演示"""
    print("🚀 Dynamic Radar Chart Dimensions Demo")
    print("=" * 70)
    print("演示求职方向变化时雷达图维度的智能调整")
    print("=" * 70)
    
    try:
        demo_career_direction_change()
        demo_dimension_comparison()
        demo_llm_vs_static()
        demo_user_skill_influence()
        
        print("\n🎉 动态雷达图维度演示完成！")
        print("\n✨ 核心特性总结:")
        print("1. ✅ 不同求职方向具有不同的技能维度")
        print("2. ✅ 雷达图维度根据岗位智能调整")
        print("3. ✅ 支持8+种常见岗位类型")
        print("4. ✅ LLM动态生成更精准的维度")
        print("5. ✅ 考虑用户技能背景的个性化维度")
        
        print("\n🎯 解决的问题:")
        print("- ❌ 之前：雷达图维度固定，不随岗位变化")
        print("- ✅ 现在：雷达图维度动态调整，精准反映岗位要求")
        
        print("\n🚀 在Gradio中的体验:")
        print("1. 用户修改目标岗位")
        print("2. 保存后雷达图自动重新生成")
        print("3. 新雷达图显示该岗位的专业维度")
        print("4. 用户技能与新维度进行对比")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
