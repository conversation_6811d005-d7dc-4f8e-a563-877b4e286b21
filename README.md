# 求职助手 AI - Career Guidance Assistant

基于大模型的智能求职规划助手，为用户提供个性化的职业规划、技能提升建议和行业分析。

## 🌟 主要功能

### 1. 个人画像管理
- 用户基本信息收集和维护
- 动态更新用户技能和经历
- 智能提取对话中的新信息

### 2. 智能职业规划 Agent
- 基于用户背景制定职业发展路径
- 短期、中期、长期目标规划
- 技能差距分析和建议

### 3. 技能提升 Agent
- 个性化学习计划制定
- 学习资源推荐
- 实践项目建议
- 学习进度跟踪

### 4. 行业专家 Agent
- 行业趋势分析
- 薪资水平参考
- 就业市场分析
- 岗位需求洞察

### 5. 可视化功能
- 技能雷达图
- 学习计划时间线
- 行业概览图表

## 🚀 快速开始

### 环境要求
- Python 3.8+
- OpenAI API Key

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd career-guidance-ai
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，添加您的 OpenAI API Key
OPENAI_API_KEY=your-openai-api-key-here
```

4. **启动应用**
```bash
python main.py
```

5. **访问应用**
打开浏览器访问：http://localhost:7860

## 📁 项目结构

```
career-guidance-ai/
├── main.py                 # 主应用入口
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── .env.example          # 环境变量模板
├── agents/               # AI Agent 模块
│   ├── chat_agent.py     # 中央调度器
│   ├── career_planning_agent.py  # 职业规划 Agent
│   ├── skill_enhancement_agent.py # 技能提升 Agent
│   └── industry_expert_agent.py   # 行业专家 Agent
├── modules/              # 核心模块
│   ├── user_profile.py   # 用户画像管理
│   └── visualization.py  # 可视化模块
└── utils/                # 工具函数
    ├── llm_client.py     # LLM API 客户端
    └── helpers.py        # 辅助函数
```

## 💡 使用指南

### 首次使用
1. 填写个人基本信息（姓名、学历、专业等）
2. 设置求职意向（目标行业、岗位等）
3. 输入已有技能和项目经历

### 对话示例

**职业规划咨询：**
```
用户：我是计算机专业大三学生，想做前端开发，请给我一个职业规划建议。
助手：[提供详细的职业发展路径和阶段性目标]
```

**技能学习：**
```
用户：我想学习React，给我一个三个月的学习计划。
助手：[制定详细的学习计划和资源推荐]
```

**行业咨询：**
```
用户：前端开发行业的薪资水平如何？
助手：[提供行业薪资分析和就业前景]
```

## 🔧 配置说明

### OpenAI API 配置
在 `.env` 文件中配置：
```env
OPENAI_API_KEY=your-api-key
OPENAI_MODEL=gpt-4          # 可选：gpt-3.5-turbo, gpt-4
OPENAI_TEMPERATURE=0.7      # 创造性参数 (0-1)
OPENAI_MAX_TOKENS=1000      # 最大输出长度
```

### 应用配置
```env
APP_PORT=7860              # 应用端口
APP_HOST=0.0.0.0          # 监听地址
DEBUG=False               # 调试模式
```

## 📊 技术架构

### 系统架构
```
Web UI (Gradio) 
    ↓
ChatAgent (中央调度)
    ↓
┌─────────────┬─────────────┬─────────────┐
│ Career      │ Skill       │ Industry    │
│ Agent       │ Agent       │ Agent       │
└─────────────┴─────────────┴─────────────┘
    ↓
OpenAI LLM API
    ↓
User Profile Storage (JSON)
```

### 核心组件
- **ChatAgent**: 中央调度器，负责意图识别和路由
- **专业 Agents**: 处理特定领域的咨询请求
- **用户画像管理**: 动态维护用户信息
- **可视化模块**: 生成图表和可视化内容

## 🛠️ 开发指南

### 添加新的 Agent
1. 在 `agents/` 目录创建新的 Agent 文件
2. 继承基础 Agent 类或参考现有实现
3. 在 `ChatAgent` 中添加路由逻辑
4. 更新意图分类关键词

### 自定义可视化
1. 在 `modules/visualization.py` 中添加新的图表类型
2. 使用 matplotlib 或 plotly 生成图表
3. 在相应 Agent 中调用可视化函数

### 扩展用户画像
1. 更新 `config.py` 中的默认画像结构
2. 修改 `modules/user_profile.py` 中的处理逻辑
3. 调整前端表单字段

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [OpenAI](https://openai.com/) - 提供强大的 LLM API
- [Gradio](https://gradio.app/) - 优秀的 Web UI 框架
- [Matplotlib](https://matplotlib.org/) - 数据可视化库

## 📞 支持

如有问题或建议，请：
1. 查看 [FAQ](docs/FAQ.md)
2. 提交 [Issue](https://github.com/your-repo/issues)
3. 联系开发团队

---

**注意**: 本项目需要 OpenAI API Key 才能正常运行。请确保您有有效的 API 访问权限。
