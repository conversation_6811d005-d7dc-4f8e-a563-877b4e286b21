"""
Career Planning Agent for Career Guidance AI Assistant
"""
import json
import logging
from typing import Dict, List, Optional
from utils.llm_client import llm_client
from utils.helpers import format_user_profile_for_prompt, split_text_and_json

logger = logging.getLogger(__name__)

class CareerPlanningAgent:
    """Specialized agent for career planning and development path guidance"""
    
    def __init__(self):
        self.system_prompt = """你是一名资深职业规划师，擅长根据用户背景、求职意向和行业趋势，为用户设计短期/中期/长期的职业发展路径。

请输出清晰可读的职业规划建议，并在最后附上一个 JSON 格式的结构化职业规划方案，格式如下：
{
  "短期目标": ["目标1", "目标2", ...],
  "中期目标": ["目标1", "目标2", ...], 
  "长期目标": ["目标1", "目标2", ...],
  "注意事项": ["注意点1", "注意点2", ...]
}

请确保建议具体可行，符合用户的实际情况和能力水平。"""
    
    def process_request(self, user_message: str, user_profile: Dict) -> Dict:
        """
        Process career planning request
        
        Args:
            user_message: User's career planning question
            user_profile: User's profile information
            
        Returns:
            Dictionary containing response text and structured plan
        """
        try:
            if not llm_client:
                return {
                    "text": "抱歉，AI服务暂时不可用，请检查API配置。",
                    "plan": {}
                }
            
            # Format user profile for prompt
            profile_text = format_user_profile_for_prompt(user_profile)
            
            # Construct user prompt
            user_prompt = f"""以下是用户的个人画像信息：
{profile_text}

用户问题：{user_message}

请根据上述信息，提供详细的职业规划建议，包括：
1. 对用户当前状况的分析
2. 适合的职业发展路径
3. 短期、中期、长期目标规划
4. 具体的行动建议和注意事项

最后请提供JSON格式的结构化规划方案。"""
            
            # Call LLM
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = llm_client.chat_completion(messages)
            
            # Split text and JSON parts
            text_part, json_part = split_text_and_json(response)
            
            # Parse JSON structure
            structured_plan = {}
            if json_part:
                try:
                    structured_plan = json.loads(json_part)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON from LLM response: {e}")
            
            return {
                "text": text_part if text_part else response,
                "plan": structured_plan
            }
            
        except Exception as e:
            logger.error(f"Error in career planning agent: {e}")
            return {
                "text": f"处理职业规划请求时出现错误：{str(e)}",
                "plan": {}
            }
    
    def generate_career_path_suggestions(self, target_position: str, user_background: Dict) -> List[str]:
        """
        Generate specific career path suggestions
        
        Args:
            target_position: User's target position
            user_background: User's background information
            
        Returns:
            List of career path suggestions
        """
        try:
            if not llm_client:
                return ["AI服务暂时不可用"]
            
            prompt = f"""基于以下信息，请提供3-5个具体的职业发展路径建议：

目标岗位：{target_position}
用户背景：{user_background}

请为每个路径提供：
1. 路径名称
2. 适合人群
3. 发展阶段
4. 关键技能要求

以简洁的列表形式输出。"""
            
            messages = [
                {"role": "system", "content": "你是职业规划专家，请提供具体可行的职业路径建议。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.6)
            
            # Split response into suggestions
            suggestions = [line.strip() for line in response.split('\n') if line.strip()]
            return suggestions[:5]  # Return top 5 suggestions
            
        except Exception as e:
            logger.error(f"Error generating career path suggestions: {e}")
            return [f"生成职业路径建议时出现错误：{str(e)}"]
    
    def analyze_skill_gaps(self, user_skills: List[str], target_position: str) -> Dict:
        """
        Analyze skill gaps between user's current skills and target position
        
        Args:
            user_skills: List of user's current skills
            target_position: Target position
            
        Returns:
            Dictionary containing skill gap analysis
        """
        try:
            if not llm_client:
                return {"analysis": "AI服务暂时不可用", "gaps": [], "recommendations": []}
            
            skills_text = ", ".join(user_skills) if user_skills else "无"
            
            prompt = f"""请分析以下技能差距：

用户当前技能：{skills_text}
目标岗位：{target_position}

请提供：
1. 技能差距分析
2. 缺失的关键技能
3. 学习优先级建议

以结构化的方式输出分析结果。"""
            
            messages = [
                {"role": "system", "content": "你是技能分析专家，请提供详细的技能差距分析。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.5)
            
            return {
                "analysis": response,
                "gaps": [],  # Could be extracted from response
                "recommendations": []  # Could be extracted from response
            }
            
        except Exception as e:
            logger.error(f"Error analyzing skill gaps: {e}")
            return {
                "analysis": f"分析技能差距时出现错误：{str(e)}",
                "gaps": [],
                "recommendations": []
            }

# Global career planning agent instance
career_agent = CareerPlanningAgent()
