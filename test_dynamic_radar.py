#!/usr/bin/env python3
"""
Test script for dynamic radar chart functionality
"""
import os
import sys

def test_static_skill_requirements():
    """Test static skill requirements for different positions"""
    print("🧪 Testing Static Skill Requirements")
    print("=" * 50)
    
    from modules.visualization import viz_manager
    
    test_positions = [
        "前端开发工程师",
        "后端开发工程师", 
        "全栈开发工程师",
        "数据分析师",
        "产品经理",
        "UI设计师",
        "测试工程师",
        "运营专员",
        "未知岗位"
    ]
    
    for position in test_positions:
        requirements = viz_manager._get_static_skill_requirements(position)
        print(f"\n📋 {position}:")
        for skill, score in requirements.items():
            print(f"   {skill}: {score}分")
    
    print("\n✅ 静态技能要求测试完成")
    return True

def test_radar_chart_generation():
    """Test radar chart generation for different positions"""
    print("\n📊 Testing Radar Chart Generation")
    print("=" * 50)
    
    from modules.visualization import viz_manager
    
    test_cases = [
        {
            "position": "前端开发工程师",
            "skills": ["HTML", "CSS", "JavaScript", "React"]
        },
        {
            "position": "后端开发工程师", 
            "skills": ["Java", "Spring", "MySQL", "Redis"]
        },
        {
            "position": "数据分析师",
            "skills": ["Python", "SQL", "Pandas", "机器学习"]
        },
        {
            "position": "产品经理",
            "skills": ["需求分析", "原型设计", "项目管理"]
        },
        {
            "position": "全栈开发工程师",
            "skills": ["JavaScript", "React", "Node.js", "MongoDB"]
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        position = case["position"]
        skills = case["skills"]
        
        print(f"\n🎯 测试案例 {i}: {position}")
        print(f"   用户技能: {', '.join(skills)}")
        
        chart_path = viz_manager.create_skill_radar_chart(skills, position)
        
        if chart_path and os.path.exists(chart_path):
            print(f"   ✅ 雷达图生成成功: {chart_path}")
            
            # Check file size to ensure it's not empty
            file_size = os.path.getsize(chart_path)
            if file_size > 1000:  # At least 1KB
                print(f"   ✅ 图表文件大小正常: {file_size} bytes")
            else:
                print(f"   ⚠️  图表文件可能异常: {file_size} bytes")
        else:
            print(f"   ❌ 雷达图生成失败")
            return False
    
    print("\n✅ 雷达图生成测试完成")
    return True

def test_position_change_impact():
    """Test how position changes affect radar chart dimensions"""
    print("\n🔄 Testing Position Change Impact")
    print("=" * 50)
    
    from modules.visualization import viz_manager
    
    # Same user skills, different positions
    user_skills = ["JavaScript", "Python", "SQL", "Git"]
    
    positions = [
        "前端开发工程师",
        "后端开发工程师",
        "数据分析师",
        "全栈开发工程师"
    ]
    
    requirements_by_position = {}
    
    for position in positions:
        requirements = viz_manager._get_static_skill_requirements(position)
        requirements_by_position[position] = requirements
        
        print(f"\n📋 {position} 的技能维度:")
        for skill, score in requirements.items():
            print(f"   {skill}: {score}分")
    
    # Check if dimensions are different
    print("\n🔍 维度差异分析:")
    
    frontend_dims = set(requirements_by_position["前端开发工程师"].keys())
    backend_dims = set(requirements_by_position["后端开发工程师"].keys())
    data_dims = set(requirements_by_position["数据分析师"].keys())
    
    print(f"前端独有维度: {frontend_dims - backend_dims - data_dims}")
    print(f"后端独有维度: {backend_dims - frontend_dims - data_dims}")
    print(f"数据分析独有维度: {data_dims - frontend_dims - backend_dims}")
    
    # Check if all positions have different dimensions
    all_same = True
    for pos1 in positions:
        for pos2 in positions:
            if pos1 != pos2:
                dims1 = set(requirements_by_position[pos1].keys())
                dims2 = set(requirements_by_position[pos2].keys())
                if dims1 == dims2:
                    print(f"⚠️  {pos1} 和 {pos2} 的维度完全相同")
                    all_same = False
    
    if all_same:
        print("✅ 不同岗位具有不同的技能维度")
    else:
        print("⚠️  部分岗位的技能维度相同")
    
    return True

def test_skill_matching():
    """Test how user skills are matched to position requirements"""
    print("\n🎯 Testing Skill Matching Logic")
    print("=" * 50)
    
    from modules.visualization import viz_manager
    
    # Test skill matching for frontend position
    position = "前端开发工程师"
    requirements = viz_manager._get_static_skill_requirements(position)
    
    test_skill_sets = [
        ["HTML", "CSS", "JavaScript"],  # Basic frontend
        ["React", "Vue", "Angular"],    # Framework skills
        ["Webpack", "Vite", "ESLint"],  # Tooling
        ["Java", "Spring", "MySQL"],    # Backend skills
        ["Photoshop", "Figma", "Sketch"], # Design skills
        []  # No skills
    ]
    
    print(f"📋 {position} 要求的维度:")
    for skill, score in requirements.items():
        print(f"   {skill}: {score}分")
    
    for i, user_skills in enumerate(test_skill_sets, 1):
        print(f"\n🧪 测试技能组合 {i}: {user_skills if user_skills else '无技能'}")
        
        # Simulate the matching logic from create_skill_radar_chart
        user_scores = {}
        for skill, requirement in requirements.items():
            # Simple matching - check if any user skill relates to requirement
            matched = False
            if user_skills:
                for user_skill in user_skills:
                    if (user_skill.lower() in skill.lower() or 
                        skill.lower() in user_skill.lower() or
                        any(keyword in user_skill.lower() for keyword in 
                            ['html', 'css', 'js', 'javascript', 'react', 'vue'] if 'javascript' in skill.lower() or '前端' in skill.lower())):
                        matched = True
                        break
            
            if matched:
                user_scores[skill] = min(requirement, 4)  # Cap at 4
            else:
                user_scores[skill] = 1  # Basic score
        
        print("   匹配结果:")
        for skill, score in user_scores.items():
            status = "✅ 匹配" if score > 1 else "❌ 未匹配"
            print(f"     {skill}: {score}分 {status}")
    
    print("\n✅ 技能匹配测试完成")
    return True

def demo_dynamic_requirements():
    """Demo the dynamic requirements generation"""
    print("\n🤖 Demo: Dynamic Requirements Generation")
    print("=" * 50)
    
    from modules.visualization import viz_manager
    
    # Test with various position types
    demo_cases = [
        {
            "position": "AI工程师",
            "skills": ["Python", "TensorFlow", "机器学习"]
        },
        {
            "position": "区块链开发工程师", 
            "skills": ["Solidity", "Web3", "智能合约"]
        },
        {
            "position": "游戏开发工程师",
            "skills": ["Unity", "C#", "3D建模"]
        },
        {
            "position": "DevOps工程师",
            "skills": ["Docker", "Kubernetes", "CI/CD"]
        }
    ]
    
    for case in demo_cases:
        position = case["position"]
        skills = case["skills"]
        
        print(f"\n🎯 {position}")
        print(f"   用户技能: {', '.join(skills)}")
        
        # Try to generate requirements (will use static fallback if no LLM)
        requirements = viz_manager._generate_skill_requirements(position, skills)
        
        print("   生成的技能维度:")
        for skill, score in requirements.items():
            print(f"     {skill}: {score}分")
    
    print("\n✅ 动态要求生成演示完成")

def main():
    """Run all dynamic radar chart tests"""
    print("🚀 Dynamic Radar Chart Testing")
    print("=" * 60)
    print("测试动态雷达图功能 - 根据求职方向变化维度")
    print("=" * 60)
    
    tests = [
        ("静态技能要求", test_static_skill_requirements),
        ("雷达图生成", test_radar_chart_generation),
        ("岗位变化影响", test_position_change_impact),
        ("技能匹配逻辑", test_skill_matching)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # Run demo
    try:
        demo_dynamic_requirements()
    except Exception as e:
        print(f"❌ 动态要求演示异常: {e}")
    
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 动态雷达图功能测试通过！")
        print("\n✨ 功能确认:")
        print("- ✅ 支持多种岗位类型的技能维度")
        print("- ✅ 不同岗位具有不同的评估维度")
        print("- ✅ 技能匹配逻辑正常工作")
        print("- ✅ 雷达图生成功能正常")
        print("- ✅ 支持动态要求生成（如有LLM）")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
