"""
Skill Enhancement Agent for Career Guidance AI Assistant
"""
import json
import logging
from typing import Dict, List, Optional
from utils.llm_client import llm_client
from utils.helpers import format_user_profile_for_prompt, split_text_and_json, parse_skill_and_duration

logger = logging.getLogger(__name__)

class SkillEnhancementAgent:
    """Specialized agent for skill learning and enhancement guidance"""
    
    def __init__(self):
        self.system_prompt = """你是资深的技能培训导师，擅长根据用户背景，将复杂的技能拆解为可执行的学习计划，并推荐合适的学习资源。

请提供详细的学习建议和时间规划，包括：
1. 学习路径分析
2. 阶段性学习目标
3. 推荐的学习资源（公开免费教程、文档、书籍等）
4. 实践项目建议
5. 学习注意事项

如果可能，请在最后提供一个JSON格式的学习计划，格式如下：
{
  "第1-2周": ["学习目标1", "学习目标2"],
  "第3-4周": ["学习目标1", "学习目标2"],
  "推荐资源": ["资源1", "资源2"],
  "实践项目": ["项目1", "项目2"]
}"""
    
    def process_request(self, user_message: str, user_profile: Dict) -> Dict:
        """
        Process skill enhancement request
        
        Args:
            user_message: User's skill learning question
            user_profile: User's profile information
            
        Returns:
            Dictionary containing response text and structured learning plan
        """
        try:
            if not llm_client:
                return {
                    "text": "抱歉，AI服务暂时不可用，请检查API配置。",
                    "plan": {}
                }
            
            # Extract target skill and duration from user message
            target_skill, duration = parse_skill_and_duration(user_message)
            
            # Format user profile for prompt
            profile_text = format_user_profile_for_prompt(user_profile)
            current_skills = user_profile.get("技能与兴趣", {}).get("已有技能", [])
            
            # Construct user prompt
            user_prompt = f"""以下是用户个人画像：
{profile_text}

用户想学习技能：{target_skill}
学习时长：{duration}
现有技能：{", ".join(current_skills) if current_skills else "无"}

用户问题：{user_message}

请根据用户的背景和现有技能水平，制定一个详细的{duration}学习计划，包括：
1. 学习路径规划
2. 每个阶段的具体学习目标
3. 推荐的学习资源和教程
4. 实践项目建议
5. 学习方法和注意事项

请确保计划具体可行，适合用户的当前水平。"""
            
            # Call LLM
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = llm_client.chat_completion(messages)
            
            # Split text and JSON parts
            text_part, json_part = split_text_and_json(response)
            
            # Parse JSON structure
            learning_plan = {}
            if json_part:
                try:
                    learning_plan = json.loads(json_part)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON from LLM response: {e}")
            
            return {
                "text": text_part if text_part else response,
                "plan": learning_plan
            }
            
        except Exception as e:
            logger.error(f"Error in skill enhancement agent: {e}")
            return {
                "text": f"处理技能学习请求时出现错误：{str(e)}",
                "plan": {}
            }
    
    def generate_learning_resources(self, skill: str, level: str = "beginner") -> List[Dict]:
        """
        Generate learning resources for a specific skill
        
        Args:
            skill: Target skill to learn
            level: Learning level (beginner, intermediate, advanced)
            
        Returns:
            List of learning resource dictionaries
        """
        try:
            if not llm_client:
                return [{"name": "AI服务暂时不可用", "type": "error", "url": ""}]
            
            prompt = f"""请为学习"{skill}"技能推荐5-8个优质的学习资源，适合{level}水平的学习者。

对每个资源，请提供：
1. 资源名称
2. 资源类型（视频教程/文档/书籍/在线课程等）
3. 简短描述
4. 推荐理由

请优先推荐免费或性价比高的资源。"""
            
            messages = [
                {"role": "system", "content": "你是学习资源推荐专家，请提供高质量的学习资源建议。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.6)
            
            # Parse response into structured format
            resources = []
            lines = response.split('\n')
            current_resource = {}
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith(('1.', '2.', '3.', '4.')):
                    if current_resource:
                        resources.append(current_resource)
                        current_resource = {}
                    current_resource['name'] = line
                    current_resource['type'] = 'resource'
                    current_resource['description'] = ''
                elif line:
                    if current_resource:
                        current_resource['description'] += line + ' '
            
            if current_resource:
                resources.append(current_resource)
            
            return resources[:8]  # Return top 8 resources
            
        except Exception as e:
            logger.error(f"Error generating learning resources: {e}")
            return [{"name": f"生成学习资源时出现错误：{str(e)}", "type": "error", "description": ""}]
    
    def create_practice_projects(self, skill: str, user_level: str) -> List[Dict]:
        """
        Create practice project suggestions for skill development
        
        Args:
            skill: Target skill
            user_level: User's current level
            
        Returns:
            List of project suggestion dictionaries
        """
        try:
            if not llm_client:
                return [{"name": "AI服务暂时不可用", "difficulty": "unknown", "description": ""}]
            
            prompt = f"""请为学习"{skill}"技能的{user_level}水平学习者，设计3-5个实践项目。

对每个项目，请提供：
1. 项目名称
2. 难度级别（初级/中级/高级）
3. 项目描述和目标
4. 主要技术要点
5. 预计完成时间
6. 学习收获

项目应该循序渐进，从简单到复杂。"""
            
            messages = [
                {"role": "system", "content": "你是项目设计专家，请提供实用的练习项目建议。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.7)
            
            # Parse response into project list
            projects = []
            sections = response.split('\n\n')
            
            for section in sections:
                if section.strip():
                    lines = section.strip().split('\n')
                    if lines:
                        project = {
                            "name": lines[0].strip(),
                            "difficulty": "中级",  # Default
                            "description": '\n'.join(lines[1:]) if len(lines) > 1 else "",
                            "duration": "1-2周"  # Default
                        }
                        projects.append(project)
            
            return projects[:5]  # Return top 5 projects
            
        except Exception as e:
            logger.error(f"Error creating practice projects: {e}")
            return [{"name": f"生成实践项目时出现错误：{str(e)}", "difficulty": "unknown", "description": ""}]
    
    def assess_skill_level(self, skill: str, user_experience: str) -> str:
        """
        Assess user's skill level based on their experience description
        
        Args:
            skill: Target skill
            user_experience: User's description of their experience
            
        Returns:
            Assessed skill level (beginner/intermediate/advanced)
        """
        try:
            if not llm_client:
                return "intermediate"  # Default fallback
            
            prompt = f"""请评估用户在"{skill}"技能方面的水平。

用户经验描述：{user_experience}

请根据描述判断用户的技能水平：
- 初级 (beginner): 刚开始学习或有基础了解
- 中级 (intermediate): 有一定实践经验，能独立完成简单项目
- 高级 (advanced): 有丰富经验，能处理复杂问题

请只回答：初级、中级或高级。"""
            
            messages = [
                {"role": "system", "content": "你是技能评估专家，请准确评估用户的技能水平。"},
                {"role": "user", "content": prompt}
            ]
            
            response = llm_client.chat_completion(messages, temperature=0.3)
            
            # Map Chinese response to English
            level_mapping = {
                "初级": "beginner",
                "中级": "intermediate", 
                "高级": "advanced"
            }
            
            for chinese, english in level_mapping.items():
                if chinese in response:
                    return english
            
            return "intermediate"  # Default fallback
            
        except Exception as e:
            logger.error(f"Error assessing skill level: {e}")
            return "intermediate"

# Global skill enhancement agent instance
skill_agent = SkillEnhancementAgent()
